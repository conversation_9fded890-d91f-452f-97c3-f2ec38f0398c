/* Categories List - Professional Inventory Management Interface */

/* CSS Custom Properties for consistent theming */
:root {
  --categories-primary-blue: #2563eb;
  --categories-secondary-blue: #3b82f6;
  --categories-light-blue: #dbeafe;
  --categories-success-green: #10b981;
  --categories-warning-yellow: #f59e0b;
  --categories-danger-red: #ef4444;
  --categories-text-primary: #1e293b;
  --categories-text-secondary: #64748b;
  --categories-text-muted: #94a3b8;
  --categories-bg-primary: #ffffff;
  --categories-bg-secondary: #f8fafc;
  --categories-bg-tertiary: #f1f5f9;
  --categories-border: #e2e8f0;
  --categories-border-light: #f1f5f9;
  --categories-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --categories-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --categories-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --categories-transition: all 0.2s ease-in-out;
  --categories-border-radius: 0.5rem;
  --categories-border-radius-lg: 0.75rem;
}

/* Main Container */
.categories-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--categories-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--categories-border);
  border-top: 3px solid var(--categories-primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: var(--categories-text-secondary);
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--categories-primary-blue);
  color: white;
  border: none;
  border-radius: var(--categories-border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--categories-transition);
}

.retry-button:hover {
  background-color: var(--categories-secondary-blue);
}

/* Header Section */
.categories-header {
  background: linear-gradient(135deg, var(--categories-primary-blue) 0%, var(--categories-secondary-blue) 100%);
  color: white;
  padding: 2rem 1.5rem;
  border-radius: var(--categories-border-radius-lg);
  margin-bottom: 2rem;
  box-shadow: var(--categories-shadow-lg);
}

.categories-header__content {
  max-width: 800px;
}

.categories-header__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.categories-header__subtitle {
  font-size: 1.125rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

/* Stats Section */
.categories-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.stat-card {
  background: var(--categories-bg-primary);
  border: 1px solid var(--categories-border);
  border-radius: var(--categories-border-radius);
  padding: 1rem 1.5rem;
  text-align: center;
  min-width: 100px;
  box-shadow: var(--categories-shadow);
}

.stat-card__value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--categories-primary-blue);
  margin-bottom: 0.25rem;
}

.stat-card__label {
  font-size: 0.875rem;
  color: var(--categories-text-secondary);
  font-weight: 500;
}

/* Controls Section */
.categories-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.categories-controls__search {
  flex: 1;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--categories-border);
  border-radius: var(--categories-border-radius);
  font-size: 0.875rem;
  color: var(--categories-text-primary);
  background-color: var(--categories-bg-primary);
  transition: var(--categories-transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--categories-primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--categories-text-muted);
  pointer-events: none;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--categories-text-secondary);
}

.empty-state__icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state__title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--categories-text-primary);
}

.empty-state__message {
  font-size: 1rem;
  margin: 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Category Card */
.category-card {
  background: var(--categories-bg-primary);
  border: 1px solid var(--categories-border);
  border-radius: var(--categories-border-radius-lg);
  overflow: hidden;
  transition: var(--categories-transition);
  box-shadow: var(--categories-shadow);
  display: flex;
  flex-direction: column;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--categories-shadow-lg);
  border-color: var(--categories-primary-blue);
}

.category-card--expanded {
  border-color: var(--categories-primary-blue);
  box-shadow: var(--categories-shadow-md);
}

.category-card__header {
  padding: 1.5rem;
  background: var(--categories-bg-secondary);
  border-bottom: 1px solid var(--categories-border);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}

.category-card__icon {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--categories-border-radius);
}

.category-card__placeholder {
  width: 100%;
  height: 100%;
  background: var(--categories-light-blue);
  border-radius: var(--categories-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--categories-primary-blue);
}

.category-card__content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-card__name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--categories-text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.category-card__slug {
  font-size: 0.875rem;
  color: var(--categories-text-secondary);
  margin: 0 0 0.75rem 0;
  font-family: 'Courier New', monospace;
  background: var(--categories-bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
}

.category-card__meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.category-card__id {
  font-size: 0.75rem;
  color: var(--categories-text-muted);
  font-weight: 500;
}

.category-card__subcategory-count {
  font-size: 0.75rem;
  color: var(--categories-primary-blue);
  font-weight: 600;
  background: var(--categories-light-blue);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

/* Subcategories Section */
.category-card__subcategories {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--categories-border-light);
}

.subcategories__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--categories-text-primary);
  margin: 0 0 0.75rem 0;
}

.subcategories__list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.subcategory-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: var(--categories-bg-tertiary);
  border-radius: var(--categories-border-radius);
  border: 1px solid var(--categories-border-light);
  transition: var(--categories-transition);
}

.subcategory-item:hover {
  background: var(--categories-light-blue);
  border-color: var(--categories-primary-blue);
}

.subcategory-item__icon {
  font-size: 0.875rem;
  color: var(--categories-primary-blue);
  opacity: 0.7;
}

.subcategory-item__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.subcategory-item__name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--categories-text-primary);
}

.subcategory-item__slug {
  font-size: 0.75rem;
  color: var(--categories-text-secondary);
  font-family: 'Courier New', monospace;
}

.subcategory-item__id {
  font-size: 0.75rem;
  color: var(--categories-text-muted);
  font-weight: 500;
}

.category-card__actions {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--categories-border-light);
  background: var(--categories-bg-secondary);
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--categories-border);
  border-radius: var(--categories-border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--categories-transition);
  background: var(--categories-bg-primary);
  color: var(--categories-text-primary);
}

.action-button--primary {
  background: var(--categories-primary-blue);
  color: white;
  border-color: var(--categories-primary-blue);
}

.action-button--primary:hover {
  background: var(--categories-secondary-blue);
  border-color: var(--categories-secondary-blue);
}

.action-button--secondary {
  background: var(--categories-bg-primary);
  color: var(--categories-text-primary);
  border-color: var(--categories-border);
}

.action-button--secondary:hover {
  background: var(--categories-bg-tertiary);
  border-color: var(--categories-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .categories-header {
    padding: 1.5rem 1rem;
  }
  
  .categories-header__title {
    font-size: 2rem;
  }
  
  .categories-stats {
    justify-content: center;
  }
  
  .categories-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .category-card__header {
    min-height: 100px;
    padding: 1rem;
  }
  
  .category-card__icon {
    width: 60px;
    height: 60px;
  }

  .category-card__meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .category-card__actions {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }

  .subcategory-item {
    padding: 0.75rem 0.5rem;
  }

  .subcategory-item__content {
    min-width: 0;
  }
}
