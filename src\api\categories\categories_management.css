/* Categories Management - Professional CRUD Interface */

/* CSS Custom Properties */
:root {
  --management-primary-blue: #2563eb;
  --management-secondary-blue: #3b82f6;
  --management-light-blue: #dbeafe;
  --management-success-green: #10b981;
  --management-warning-yellow: #f59e0b;
  --management-danger-red: #ef4444;
  --management-text-primary: #1e293b;
  --management-text-secondary: #64748b;
  --management-text-muted: #94a3b8;
  --management-bg-primary: #ffffff;
  --management-bg-secondary: #f8fafc;
  --management-bg-tertiary: #f1f5f9;
  --management-border: #e2e8f0;
  --management-border-light: #f1f5f9;
  --management-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --management-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --management-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --management-transition: all 0.2s ease-in-out;
  --management-border-radius: 0.5rem;
  --management-border-radius-lg: 0.75rem;
}

/* Main Container */
.management-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

/* Loading and Error States */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--management-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--management-border);
  border-top: 3px solid var(--management-primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--management-primary-blue);
  color: white;
  border: none;
  border-radius: var(--management-border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--management-transition);
}

.retry-button:hover {
  background-color: var(--management-secondary-blue);
}

/* Header Section */
.management-header {
  background: linear-gradient(135deg, var(--management-primary-blue) 0%, var(--management-secondary-blue) 100%);
  color: white;
  padding: 2rem 1.5rem;
  border-radius: var(--management-border-radius-lg);
  margin-bottom: 2rem;
  box-shadow: var(--management-shadow-lg);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  flex-wrap: wrap;
  gap: 1rem;
}

.management-header__content {
  flex: 1;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: var(--management-border-radius);
  cursor: pointer;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  transition: var(--management-transition);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.management-header__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.management-header__subtitle {
  font-size: 1.125rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.management-header__actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Error Banner */
.error-banner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--management-border-radius);
  margin-bottom: 1.5rem;
  box-shadow: var(--management-shadow);
}

.error-banner__content {
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-banner__icon {
  color: var(--management-danger-red);
  font-size: 1.25rem;
}

.error-banner__message {
  flex: 1;
  color: #991b1b;
  font-weight: 500;
  font-size: 0.875rem;
}

.error-banner__close {
  background: none;
  border: none;
  color: #991b1b;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--management-transition);
}

.error-banner__close:hover {
  background: rgba(153, 27, 27, 0.1);
}

/* Success Banner */
.success-banner {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: var(--management-border-radius);
  margin-bottom: 1.5rem;
  box-shadow: var(--management-shadow);
}

.success-banner__content {
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.success-banner__icon {
  color: var(--management-success-green);
  font-size: 1.25rem;
}

.success-banner__message {
  flex: 1;
  color: #166534;
  font-weight: 500;
  font-size: 0.875rem;
}

.success-banner__close {
  background: none;
  border: none;
  color: #166534;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--management-transition);
}

.success-banner__close:hover {
  background: rgba(22, 101, 52, 0.1);
}

/* Stats Section */
.management-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.stat-card {
  background: var(--management-bg-primary);
  border: 1px solid var(--management-border);
  border-radius: var(--management-border-radius);
  padding: 1rem 1.5rem;
  text-align: center;
  min-width: 100px;
  box-shadow: var(--management-shadow);
}

.stat-card__value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--management-primary-blue);
  margin-bottom: 0.25rem;
}

.stat-card__label {
  font-size: 0.875rem;
  color: var(--management-text-secondary);
  font-weight: 500;
}

/* Action Buttons */
.action-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--management-border);
  border-radius: var(--management-border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--management-transition);
  background: var(--management-bg-primary);
  color: var(--management-text-primary);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button--primary {
  background: var(--management-primary-blue);
  color: white;
  border-color: var(--management-primary-blue);
}

.action-button--primary:hover {
  background: var(--management-secondary-blue);
  border-color: var(--management-secondary-blue);
}

.action-button--secondary {
  background: var(--management-bg-primary);
  color: var(--management-text-primary);
  border-color: var(--management-border);
}

.action-button--secondary:hover {
  background: var(--management-bg-tertiary);
  border-color: var(--management-text-secondary);
}

.action-button--danger {
  background: var(--management-danger-red);
  color: white;
  border-color: var(--management-danger-red);
}

.action-button--danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.action-button--warning {
  background: var(--management-warning-yellow);
  color: white;
  border-color: var(--management-warning-yellow);
}

.action-button--warning:hover {
  background: #d97706;
  border-color: #d97706;
}

.action-button--small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Icon Buttons */
.icon-button {
  width: 2rem;
  height: 2rem;
  border: 1px solid var(--management-border);
  border-radius: var(--management-border-radius);
  background: var(--management-bg-primary);
  color: var(--management-text-primary);
  cursor: pointer;
  transition: var(--management-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.icon-button:hover {
  background: var(--management-bg-tertiary);
}

.icon-button--primary {
  background: var(--management-primary-blue);
  color: white;
  border-color: var(--management-primary-blue);
}

.icon-button--primary:hover {
  background: var(--management-secondary-blue);
}

.icon-button--success {
  background: var(--management-success-green);
  color: white;
  border-color: var(--management-success-green);
}

.icon-button--success:hover {
  background: #059669;
}

.icon-button--danger {
  background: var(--management-danger-red);
  color: white;
  border-color: var(--management-danger-red);
}

.icon-button--danger:hover {
  background: #dc2626;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--management-text-secondary);
  background: var(--management-bg-primary);
  border: 1px solid var(--management-border);
  border-radius: var(--management-border-radius-lg);
  box-shadow: var(--management-shadow);
}

.empty-state__icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--management-text-primary);
}

.empty-state p {
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Categories Management List */
.categories-management-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.category-management-item {
  background: var(--management-bg-primary);
  border: 1px solid var(--management-border);
  border-radius: var(--management-border-radius-lg);
  box-shadow: var(--management-shadow);
  overflow: hidden;
  transition: var(--management-transition);
}

.category-management-item:hover {
  box-shadow: var(--management-shadow-md);
}

.category-item-header {
  padding: 1.5rem;
  background: var(--management-bg-secondary);
  border-bottom: 1px solid var(--management-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.category-item-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.category-item-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--management-text-primary);
  margin: 0;
}

.category-item-slug {
  font-size: 0.875rem;
  color: var(--management-text-secondary);
  font-family: 'Courier New', monospace;
  background: var(--management-bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.category-item-count {
  font-size: 0.75rem;
  color: var(--management-primary-blue);
  font-weight: 600;
  background: var(--management-light-blue);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.category-item-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Subcategories Section */
.subcategories-section {
  padding: 1.5rem;
  background: var(--management-bg-primary);
}

.subcategories-empty {
  text-align: center;
  padding: 2rem;
  color: var(--management-text-secondary);
}

.subcategories-empty p {
  margin: 0 0 1rem 0;
}

.subcategories-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subcategory-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--management-bg-tertiary);
  border: 1px solid var(--management-border-light);
  border-radius: var(--management-border-radius);
  transition: var(--management-transition);
}

.subcategory-item:hover {
  background: var(--management-light-blue);
  border-color: var(--management-primary-blue);
}

.subcategory-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.subcategory-name {
  font-size: 1rem;
  font-weight: 500;
  color: var(--management-text-primary);
}

.subcategory-slug {
  font-size: 0.875rem;
  color: var(--management-text-secondary);
  font-family: 'Courier New', monospace;
}

.subcategory-actions {
  display: flex;
  gap: 0.5rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--management-bg-primary);
  border-radius: var(--management-border-radius-lg);
  box-shadow: var(--management-shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content--danger {
  border-top: 4px solid var(--management-danger-red);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--management-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--management-text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--management-text-muted);
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--management-transition);
}

.modal-close:hover {
  background: var(--management-bg-tertiary);
  color: var(--management-text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--management-border);
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--management-text-primary);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--management-border);
  border-radius: var(--management-border-radius);
  font-size: 0.875rem;
  color: var(--management-text-primary);
  background: var(--management-bg-primary);
  transition: var(--management-transition);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--management-primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.warning-text {
  color: var(--management-danger-red);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    align-items: stretch;
  }

  .management-header__actions {
    justify-content: stretch;
  }

  .management-header__actions .action-button {
    flex: 1;
    justify-content: center;
  }

  .management-stats {
    justify-content: center;
  }

  .category-item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .category-item-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .category-item-actions {
    justify-content: center;
  }

  .subcategory-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .subcategory-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .subcategory-actions {
    justify-content: center;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .action-button {
    width: 100%;
    justify-content: center;
  }
}
