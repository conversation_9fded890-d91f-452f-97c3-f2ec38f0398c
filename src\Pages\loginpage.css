/* Login Page - Styled to match product detail/products list */
.login-container {
  max-width: 400px;
  margin: 4rem auto;
  background: var(--products-bg-primary, #fff);
  border-radius: var(--products-border-radius, 0.5rem);
  box-shadow: var(--products-shadow, 0 1px 3px 0 rgba(0,0,0,0.1));
  padding: 2.5rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-title {
  color: var(--products-primary-blue, #2563eb);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  letter-spacing: -1px;
}

.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.login-input {
  padding: 0.75rem 1rem;
  border: 1px solid var(--products-border, #e2e8f0);
  border-radius: var(--products-border-radius, 0.5rem);
  font-size: 1rem;
  color: var(--products-text-primary, #1e293b);
  background: var(--products-bg-secondary, #f8fafc);
  transition: var(--products-transition, all 0.2s ease-in-out);
}

.login-input:focus {
  outline: none;
  border-color: var(--products-primary-blue, #2563eb);
  background: var(--products-bg-tertiary, #f1f5f9);
}

.login-button {
  background: var(--products-primary-blue, #2563eb);
  color: #fff;
  border: none;
  border-radius: var(--products-border-radius, 0.5rem);
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--products-transition, all 0.2s ease-in-out);
  margin-top: 0.5rem;
}

.login-button:hover {
  background: var(--products-secondary-blue, #3b82f6);
}

.login-error {
  color: var(--products-danger-red, #ef4444);
  margin-top: 1rem;
  font-size: 1rem;
  text-align: center;
  font-weight: 500;
}
