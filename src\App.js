import {BrowserRouter as Router,Routes,Route} from 'react-router-dom';
import Header from './Components/Header';
import Home from './Components/Home';
import './App.css';
import Products from './api/products/products_list'
import ProductDetail from './api/products/product_detail'
import Orders from './api/orders/orders_list'
import OrderDetail from './api/orders/order_detail'
import Categories from './api/categories/categories_list'
import Loginpage from './Pages/loginpage';
import Logoutpage from './Pages/logoutpage';
import ProductAdd from './api/products/product_add';
import OrderAdd from './api/orders/order_add';

function App() {
  return (
    <Router>
      <Header />
      <main className="main-content">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/products" element={<Products />} />
          <Route path="/products/:id" element={<ProductDetail />} />
          <Route path="/categories" element={<Categories />} />
          <Route path="/orders" element={<Orders />} />
          <Route path="/orders/pending" element={<Orders />} />
          <Route path="/orders/processing" element={<Orders />} />
          <Route path="/orders/completed" element={<Orders />} />
          <Route path="/orders/detail/:id" element={<OrderDetail />} />
          <Route path="/login"    element={<Loginpage />} />
          <Route path="/logout" element = {<Logoutpage />} />
          <Route path='/products/add' element = {<ProductAdd/>}/>
          <Route path='/orders/add' element = {<OrderAdd/>}/>

        </Routes>
      </main>
    </Router>
  );
}

export default App;