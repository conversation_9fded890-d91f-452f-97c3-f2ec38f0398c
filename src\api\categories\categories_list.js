import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "./categories_list.css";

function Categories() {
  const [categories, setCategories] = useState([]);
  const [subcategories, setSubcategories] = useState([]);
  const [expandedCategories, setExpandedCategories] = useState(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem("access_token");

        // Fetch categories and subcategories in parallel
        const [categoriesResponse, subcategoriesResponse] = await Promise.all([
          fetch("https://ecommerce.techtrendo.com/api/categories/", {
            headers: {
              ...(token ? { Authorization: `Bearer ${token}` } : {})
            }
          }),
          fetch("https://ecommerce.techtrendo.com/api/subcategories/", {
            headers: {
              ...(token ? { Authorization: `Bearer ${token}` } : {})
            }
          })
        ]);

        if (!categoriesResponse.ok) {
          throw new Error(`Failed to fetch categories: ${categoriesResponse.status}`);
        }

        if (!subcategoriesResponse.ok) {
          throw new Error(`Failed to fetch subcategories: ${subcategoriesResponse.status}`);
        }

        const categoriesData = await categoriesResponse.json();
        const subcategoriesData = await subcategoriesResponse.json();

        // Handle different response formats for categories
        let categoriesArray = [];
        if (Array.isArray(categoriesData)) {
          categoriesArray = categoriesData;
        } else if (categoriesData && Array.isArray(categoriesData.results)) {
          categoriesArray = categoriesData.results;
        }

        // Handle different response formats for subcategories
        let subcategoriesArray = [];
        if (Array.isArray(subcategoriesData)) {
          subcategoriesArray = subcategoriesData;
        } else if (subcategoriesData && Array.isArray(subcategoriesData.results)) {
          subcategoriesArray = subcategoriesData.results;
        }

        setCategories(categoriesArray);
        setSubcategories(subcategoriesArray);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(err.message);
        setCategories([]);
        setSubcategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Helper function to get subcategories for a specific category
  const getSubcategoriesForCategory = (categoryId) => {
    return subcategories.filter(subcategory => subcategory.category === categoryId);
  };

  // Helper function to toggle category expansion
  const toggleCategoryExpansion = (categoryId) => {
    const newExpandedCategories = new Set(expandedCategories);
    if (newExpandedCategories.has(categoryId)) {
      newExpandedCategories.delete(categoryId);
    } else {
      newExpandedCategories.add(categoryId);
    }
    setExpandedCategories(newExpandedCategories);
  };

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Loading state
  if (loading) {
    return (
      <div className="categories-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading categories...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="categories-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Error Loading Categories</h2>
          <p>{error}</p>
          <button 
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="categories-container">
      {/* Header Section */}
      <div className="categories-header">
        <div className="categories-header__content">
          <h1 className="categories-header__title">Categories</h1>
          <p className="categories-header__subtitle">
            Manage your product categories
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="categories-stats">
        <div className="stat-card">
          <div className="stat-card__value">{categories.length}</div>
          <div className="stat-card__label">Total Categories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{subcategories.length}</div>
          <div className="stat-card__label">Total Subcategories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{filteredCategories.length}</div>
          <div className="stat-card__label">Filtered Results</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{expandedCategories.size}</div>
          <div className="stat-card__label">Expanded Categories</div>
        </div>
      </div>

      {/* Controls Section */}
      <div className="categories-controls">
        <div className="categories-controls__search">
          <div className="search-input-container">
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      {filteredCategories.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state__icon">🏷️</div>
          <h2 className="empty-state__title">No Categories Found</h2>
          <p className="empty-state__message">
            {searchTerm
              ? "Try adjusting your search criteria."
              : "No categories are available in your system."}
          </p>
        </div>
      ) : (
        <div className="categories-grid">
          {filteredCategories.map(category => {
            const categorySubcategories = getSubcategoriesForCategory(category.id);
            const isExpanded = expandedCategories.has(category.id);

            return (
              <div key={category.id} className={`category-card ${isExpanded ? 'category-card--expanded' : ''}`}>
                <div className="category-card__header">
                  <div className="category-card__icon">
                    {category.image ? (
                      <img
                        src={category.image}
                        alt={category.name}
                        className="category-card__image"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div
                      className="category-card__placeholder"
                      style={{ display: category.image ? 'none' : 'flex' }}
                    >
                      🏷️
                    </div>
                  </div>
                </div>

                <div className="category-card__content">
                  <div className="category-card__main-info">
                    <h3 className="category-card__name">{category.name}</h3>
                    <p className="category-card__slug">/{category.slug}</p>
                    <div className="category-card__meta">
                      <span className="category-card__id">ID: {category.id}</span>
                      <span className="category-card__subcategory-count">
                        {categorySubcategories.length} subcategories
                      </span>
                    </div>
                  </div>

                  {/* Subcategories Section */}
                  {isExpanded && categorySubcategories.length > 0 && (
                    <div className="category-card__subcategories">
                      <h4 className="subcategories__title">Subcategories:</h4>
                      <div className="subcategories__list">
                        {categorySubcategories.map(subcategory => (
                          <div key={subcategory.id} className="subcategory-item">
                            <div className="subcategory-item__icon">🏷️</div>
                            <div className="subcategory-item__content">
                              <span className="subcategory-item__name">{subcategory.name}</span>
                              <span className="subcategory-item__slug">/{subcategory.slug}</span>
                            </div>
                            <div className="subcategory-item__id">#{subcategory.id}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="category-card__actions">
                  {categorySubcategories.length > 0 && (
                    <button
                      className="action-button action-button--secondary"
                      onClick={() => toggleCategoryExpansion(category.id)}
                    >
                      {isExpanded ? '▲ Collapse' : '▼ Expand'} ({categorySubcategories.length})
                    </button>
                  )}
                  <button
                    className="action-button action-button--primary"
                    onClick={() => {
                      // Future: Navigate to category details or edit page
                      console.log('View category:', category.id);
                    }}
                  >
                    View Details
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default Categories;
