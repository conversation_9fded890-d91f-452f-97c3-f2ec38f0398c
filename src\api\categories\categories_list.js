import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "./categories_list.css";

function Categories() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const token = localStorage.getItem("access_token");
        const response = await fetch("https://ecommerce.techtrendo.com/api/categories/", {
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {})
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch categories: ${response.status}`);
        }

        const data = await response.json();
        
        // Handle different response formats
        let categoriesArray = [];
        if (Array.isArray(data)) {
          categoriesArray = data;
        } else if (data && Array.isArray(data.results)) {
          categoriesArray = data.results;
        }
        
        setCategories(categoriesArray);
      } catch (err) {
        console.error("Error fetching categories:", err);
        setError(err.message);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Loading state
  if (loading) {
    return (
      <div className="categories-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading categories...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="categories-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Error Loading Categories</h2>
          <p>{error}</p>
          <button 
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="categories-container">
      {/* Header Section */}
      <div className="categories-header">
        <div className="categories-header__content">
          <h1 className="categories-header__title">Categories</h1>
          <p className="categories-header__subtitle">
            Manage your product categories
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="categories-stats">
        <div className="stat-card">
          <div className="stat-card__value">{categories.length}</div>
          <div className="stat-card__label">Total Categories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{filteredCategories.length}</div>
          <div className="stat-card__label">Filtered Results</div>
        </div>
      </div>

      {/* Controls Section */}
      <div className="categories-controls">
        <div className="categories-controls__search">
          <div className="search-input-container">
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      {filteredCategories.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state__icon">🏷️</div>
          <h2 className="empty-state__title">No Categories Found</h2>
          <p className="empty-state__message">
            {searchTerm
              ? "Try adjusting your search criteria."
              : "No categories are available in your system."}
          </p>
        </div>
      ) : (
        <div className="categories-grid">
          {filteredCategories.map(category => (
            <div key={category.id} className="category-card">
              <div className="category-card__header">
                <div className="category-card__icon">
                  {category.image ? (
                    <img 
                      src={category.image} 
                      alt={category.name}
                      className="category-card__image"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div 
                    className="category-card__placeholder"
                    style={{ display: category.image ? 'none' : 'flex' }}
                  >
                    🏷️
                  </div>
                </div>
              </div>
              
              <div className="category-card__content">
                <h3 className="category-card__name">{category.name}</h3>
                <p className="category-card__slug">/{category.slug}</p>
                <div className="category-card__id">ID: {category.id}</div>
              </div>
              
              <div className="category-card__actions">
                <button 
                  className="action-button action-button--primary"
                  onClick={() => {
                    // Future: Navigate to category details or edit page
                    console.log('View category:', category.id);
                  }}
                >
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default Categories;
