/* Orders List - Professional Inventory Management Interface */

/* CSS Custom Properties for consistent theming */
:root {
  --orders-primary-blue: #2563eb;
  --orders-secondary-blue: #3b82f6;
  --orders-light-blue: #dbeafe;
  --orders-success-green: #10b981;
  --orders-warning-yellow: #f59e0b;
  --orders-danger-red: #ef4444;
  --orders-processing-purple: #8b5cf6;
  --orders-text-primary: #1e293b;
  --orders-text-secondary: #64748b;
  --orders-text-muted: #94a3b8;
  --orders-bg-primary: #ffffff;
  --orders-bg-secondary: #f8fafc;
  --orders-bg-tertiary: #f1f5f9;
  --orders-border: #e2e8f0;
  --orders-border-light: #f1f5f9;
  --orders-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --orders-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --orders-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --orders-transition: all 0.2s ease-in-out;
  --orders-border-radius: 0.5rem;
  --orders-border-radius-lg: 0.75rem;
}

/* Main Container */
.orders-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--orders-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--orders-border);
  border-top: 3px solid var(--orders-primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: var(--orders-text-secondary);
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-container h2 {
  color: var(--orders-text-primary);
  margin-bottom: 0.5rem;
}

.retry-button {
  background-color: var(--orders-primary-blue);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  transition: var(--orders-transition);
}

.retry-button:hover {
  background-color: var(--orders-secondary-blue);
}

/* Header Section */
.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--orders-border);
}

.orders-header__title-section {
  flex: 1;
}

.orders-header__title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--orders-text-primary);
  margin: 0 0 0.5rem 0;
}

.orders-header__subtitle {
  color: var(--orders-text-secondary);
  margin: 0;
  font-size: 1rem;
}

.orders-header__stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  background: var(--orders-bg-primary);
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius);
  padding: 1rem 1.5rem;
  text-align: center;
  min-width: 100px;
  box-shadow: var(--orders-shadow);
}

.stat-card__value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--orders-primary-blue);
  margin-bottom: 0.25rem;
}

.stat-card__label {
  font-size: 0.875rem;
  color: var(--orders-text-secondary);
  font-weight: 500;
}

/* Filter Navigation */
.orders-filter-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background-color: var(--orders-bg-secondary);
  border-radius: var(--orders-border-radius-lg);
  border: 1px solid var(--orders-border);
}

.filter-nav-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--orders-text-secondary);
  transition: var(--orders-transition);
  flex: 1;
  justify-content: center;
}

.filter-nav-button:hover {
  background-color: var(--orders-bg-primary);
  color: var(--orders-text-primary);
}

.filter-nav-button.active {
  background-color: var(--orders-primary-blue);
  color: white;
  box-shadow: var(--orders-shadow);
}

.filter-nav-icon {
  font-size: 1rem;
}

/* Controls Section */
.orders-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.orders-controls__search {
  flex: 1;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: var(--orders-text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius);
  font-size: 0.875rem;
  background-color: var(--orders-bg-primary);
  transition: var(--orders-transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--orders-primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.orders-controls__filters {
  display: flex;
  gap: 0.75rem;
}

.control-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius);
  background-color: var(--orders-bg-primary);
  font-size: 0.875rem;
  color: var(--orders-text-primary);
  cursor: pointer;
  transition: var(--orders-transition);
  min-width: 150px;
}

.control-select:focus {
  outline: none;
  border-color: var(--orders-primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Orders Grid */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Order Card */
.order-card {
  background: var(--orders-bg-primary);
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius-lg);
  overflow: hidden;
  transition: var(--orders-transition);
  cursor: pointer;
  box-shadow: var(--orders-shadow);
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--orders-shadow-lg);
  border-color: var(--orders-primary-blue);
}

.order-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem 1.25rem 0 1.25rem;
  margin-bottom: 1rem;
}

.order-card__customer {
  flex: 1;
}

.order-card__customer-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--orders-text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.order-card__order-id {
  font-size: 0.75rem;
  color: var(--orders-text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.order-card__status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--orders-border-radius);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.order-card__status--pending {
  background-color: var(--orders-warning-yellow);
  color: white;
}

.order-card__status--processing {
  background-color: var(--orders-processing-purple);
  color: white;
}

.order-card__status--completed {
  background-color: var(--orders-success-green);
  color: white;
}

.order-card__status--unknown {
  background-color: var(--orders-text-muted);
  color: white;
}

.order-card__status-icon {
  font-size: 0.875rem;
}

.order-card__content {
  padding: 0 1.25rem 1rem 1.25rem;
}

.order-card__email,
.order-card__address {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.75rem;
}

.order-card__label {
  font-size: 0.75rem;
  color: var(--orders-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-weight: 600;
}

.order-card__value {
  color: var(--orders-text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.order-card__details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: var(--orders-bg-secondary);
  border-top: 1px solid var(--orders-border-light);
}

.order-card__amount,
.order-card__items {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.order-card__amount-label,
.order-card__items-label {
  font-size: 0.75rem;
  color: var(--orders-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.order-card__amount-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--orders-primary-blue);
}

.order-card__items-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--orders-text-primary);
}

.order-card__footer {
  padding: 0.75rem 1.25rem;
  border-top: 1px solid var(--orders-border-light);
  background-color: var(--orders-bg-tertiary);
}

.order-card__date {
  font-size: 0.75rem;
  color: var(--orders-text-muted);
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--orders-text-secondary);
}

.empty-state__icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--orders-text-primary);
  margin-bottom: 0.5rem;
}

.empty-state__message {
  font-size: 1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .orders-header {
    flex-direction: column;
    gap: 1.5rem;
  }

  .orders-header__stats {
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .stat-card {
    flex: 1;
    min-width: calc(50% - 0.5rem);
  }

  .orders-filter-nav {
    flex-wrap: wrap;
  }

  .filter-nav-button {
    flex: 1;
    min-width: calc(50% - 0.25rem);
  }

  .orders-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .orders-controls__filters {
    width: 100%;
  }

  .control-select {
    flex: 1;
    min-width: auto;
  }

  .orders-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .orders-header__stats {
    flex-direction: column;
    gap: 0.75rem;
  }

  .stat-card {
    min-width: auto;
  }

  .filter-nav-button {
    min-width: auto;
    flex: 1;
  }

  .orders-controls__filters {
    flex-direction: column;
  }

  .order-card__header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .order-card__details {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
