/* Products List - Professional Inventory Management Interface */

/* CSS Custom Properties for consistent theming */
:root {
  --products-primary-blue: #2563eb;
  --products-secondary-blue: #3b82f6;
  --products-light-blue: #dbeafe;
  --products-success-green: #10b981;
  --products-warning-yellow: #f59e0b;
  --products-danger-red: #ef4444;
  --products-text-primary: #1e293b;
  --products-text-secondary: #64748b;
  --products-text-muted: #94a3b8;
  --products-bg-primary: #ffffff;
  --products-bg-secondary: #f8fafc;
  --products-bg-tertiary: #f1f5f9;
  --products-border: #e2e8f0;
  --products-border-light: #f1f5f9;
  --products-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --products-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --products-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --products-transition: all 0.2s ease-in-out;
  --products-border-radius: 0.5rem;
  --products-border-radius-lg: 0.75rem;
}

/* Main Container */
.products-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--products-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--products-border);
  border-top: 3px solid var(--products-primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: var(--products-text-secondary);
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-container h2 {
  color: var(--products-text-primary);
  margin-bottom: 0.5rem;
}

.retry-button {
  background-color: var(--products-primary-blue);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--products-border-radius);
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  transition: var(--products-transition);
}

.retry-button:hover {
  background-color: var(--products-secondary-blue);
}

/* Header Section */
.products-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--products-border);
}

.products-header__title-section {
  flex: 1;
}

.products-header__title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--products-text-primary);
  margin: 0 0 0.5rem 0;
}

.products-header__subtitle {
  color: var(--products-text-secondary);
  margin: 0;
  font-size: 1rem;
}

.products-header__stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  background: var(--products-bg-primary);
  border: 1px solid var(--products-border);
  border-radius: var(--products-border-radius);
  padding: 1rem 1.5rem;
  text-align: center;
  min-width: 100px;
  box-shadow: var(--products-shadow);
}

.stat-card__value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--products-primary-blue);
  margin-bottom: 0.25rem;
}

.stat-card__label {
  font-size: 0.875rem;
  color: var(--products-text-secondary);
  font-weight: 500;
}

/* Controls Section */
.products-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.products-controls__search {
  flex: 1;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: var(--products-text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--products-border);
  border-radius: var(--products-border-radius);
  font-size: 0.875rem;
  background-color: var(--products-bg-primary);
  transition: var(--products-transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--products-primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.products-controls__filters {
  display: flex;
  gap: 0.75rem;
}

.control-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--products-border);
  border-radius: var(--products-border-radius);
  background-color: var(--products-bg-primary);
  font-size: 0.875rem;
  color: var(--products-text-primary);
  cursor: pointer;
  transition: var(--products-transition);
  min-width: 150px;
}

.control-select:focus {
  outline: none;
  border-color: var(--products-primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Product Card */
.product-card {
  background: var(--products-bg-primary);
  border: 1px solid var(--products-border);
  border-radius: var(--products-border-radius-lg);
  overflow: hidden;
  transition: var(--products-transition);
  cursor: pointer;
  box-shadow: var(--products-shadow);
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--products-shadow-lg);
  border-color: var(--products-primary-blue);
}

.product-card__image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
  background-color: var(--products-bg-secondary);
}

.product-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--products-transition);
}

.product-card:hover .product-card__image {
  transform: scale(1.05);
}

.product-card__badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background-color: var(--products-warning-yellow);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.product-card__stock-status {
  position: absolute;
  bottom: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.product-card__stock-status--in-stock {
  background-color: var(--products-success-green);
  color: white;
}

.product-card__stock-status--low-stock {
  background-color: var(--products-warning-yellow);
  color: white;
}

.product-card__stock-status--out-of-stock {
  background-color: var(--products-danger-red);
  color: white;
}

.product-card__stock-status--unavailable {
  background-color: var(--products-text-muted);
  color: white;
}

.product-card__content {
  padding: 1.25rem;
}

.product-card__header {
  margin-bottom: 0.75rem;
}

.product-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--products-text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.product-card__sku {
  font-size: 0.75rem;
  color: var(--products-text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.product-card__description {
  color: var(--products-text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.product-card__details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--products-border-light);
}

.product-card__price-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.product-card__price {
  font-weight: 600;
}

.product-card__price--current {
  color: var(--products-primary-blue);
  font-size: 1.125rem;
}

.product-card__price--discounted {
  color: var(--products-text-muted);
  text-decoration: line-through;
  font-size: 0.875rem;
}

.product-card__stock {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.product-card__stock-label {
  font-size: 0.75rem;
  color: var(--products-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.product-card__stock-value {
  font-weight: 600;
  color: var(--products-text-primary);
}

.product-card__stock-value.low {
  color: var(--products-danger-red);
}

.product-card__meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-card__category,
.product-card__brand {
  font-size: 0.75rem;
  color: var(--products-text-muted);
  background-color: var(--products-bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--products-text-secondary);
}

.empty-state__icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--products-text-primary);
  margin-bottom: 0.5rem;
}

.empty-state__message {
  font-size: 1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .products-header {
    flex-direction: column;
    gap: 1.5rem;
  }

  .products-header__stats {
    width: 100%;
    justify-content: space-between;
  }

  .stat-card {
    flex: 1;
    min-width: auto;
  }

  .products-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .products-controls__filters {
    width: 100%;
  }

  .control-select {
    flex: 1;
    min-width: auto;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .products-header__stats {
    flex-direction: column;
    gap: 0.75rem;
  }

  .products-controls__filters {
    flex-direction: column;
  }
}
