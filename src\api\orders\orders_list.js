import { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import "./orders_list.css";
import { Link } from 'react-router-dom';

function Orders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("created_at");
  const navigate = useNavigate();
  const location = useLocation();
  const { status } = useParams(); // Get status from URL params (pending, completed)

  // Debug logging
  useEffect(() => {
    console.log('Current URL status parameter:', status);
    console.log('Current pathname:', location.pathname);
    console.log('Current filter:', getCurrentFilter());
  }, [status, location.pathname]);

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    fetch("https://ecommerce.techtrendo.com/api/orders/", {
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {})
      }
    })
      .then((res) => {
        if (!res.ok) throw new Error("Unauthorized or error fetching orders");
        return res.json();
      })
      .then((data) => {
        if (Array.isArray(data)) {
          console.log('Orders loaded:', data.length);
          console.log('Order statuses:', data.map(o => ({ id: o.id, status: o.status })));
          setOrders(data);
        } else {
          setOrders([]);
        }
      })
      .catch((err) => setError(err))
      .finally(() => setLoading(false));
  }, []);

  // Get current filter based on URL
  const getCurrentFilter = () => {
    // First try to get from URL params
    if (status === "pending") return "pending";
    if (status === "completed") return "completed";
    if (status === "processing") return "processing";

    // Fallback to checking pathname directly
    if (location.pathname === "/orders/pending") return "pending";
    if (location.pathname === "/orders/completed") return "completed";
    if (location.pathname === "/orders/processing") return "processing";

    return "all";
  };

  // Filter and sort orders
  const filteredAndSortedOrders = orders
    .filter(order => {
      const matchesSearch = order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           order.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           order.address.toLowerCase().includes(searchTerm.toLowerCase());

      const currentFilter = getCurrentFilter();

      // Debug logging
      if (orders.length > 0 && orders.indexOf(order) === 0) {
        console.log('Filtering with:', { currentFilter, orderStatus: order.status, matchesSearch });
      }

      if (currentFilter === "all") return matchesSearch;
      if (currentFilter === "pending") return matchesSearch && order.status === "pending";
      if (currentFilter === "processing") return matchesSearch && order.status === "processing";
      if (currentFilter === "completed") return matchesSearch && order.status === "completed";
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "customer_name":
          return a.customer_name.localeCompare(b.customer_name);
        case "total_amount":
          return parseFloat(b.total_amount) - parseFloat(a.total_amount);
        case "status":
          return a.status.localeCompare(b.status);
        case "created_at":
          // If created_at is available, sort by it, otherwise use id as fallback
          if (a.created_at && b.created_at) {
            return new Date(b.created_at) - new Date(a.created_at);
          }
          return b.id - a.id;
        default:
          return 0;
      }
    });

  const handleOrderClick = (order) => {
    // Navigate to order detail page
    navigate(`/orders/detail/${order.id}`, { state: { order } });
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case "pending":
        return { text: "Pending", className: "pending", icon: "⏳" };
      case "processing":
        return { text: "Processing", className: "processing", icon: "🔄" };
      case "completed":
        return { text: "Completed", className: "completed", icon: "✅" };
      default:
        return { text: "Unknown", className: "unknown", icon: "❓" };
    }
  };

  const getTotalItems = (items) => {
    if (!items || !Array.isArray(items)) return 0;
    return items.reduce((total, item) => total + (item.quantity || 0), 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFilterTitle = () => {
    const currentFilter = getCurrentFilter();
    switch (currentFilter) {
      case "pending":
        return "Pending Orders";
      case "processing":
        return "Processing Orders";
      case "completed":
        return "Completed Orders";
      default:
        return "All Orders";
    }
  };

  const getFilterDescription = () => {
    const currentFilter = getCurrentFilter();
    switch (currentFilter) {
      case "pending":
        return "Orders awaiting processing";
      case "processing":
        return "Orders currently being processed";
      case "completed":
        return "Successfully completed orders";
      default:
        return "Manage and view all customer orders";
    }
  };

  if (loading) {
    return (
      <div className="orders-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading orders...</p>
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <div className="orders-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Error Loading Orders</h2>
          <p>Unable to fetch orders. Please try again later.</p>
          <button
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const currentFilter = getCurrentFilter();
  const pendingCount = orders.filter(o => o.status === "pending").length;
  const processingCount = orders.filter(o => o.status === "processing").length;
  const completedCount = orders.filter(o => o.status === "completed").length;

  return (
    <div className="orders-container">
      {/* Header Section */}
      <div className="orders-header">
        <div className="orders-header__title-section">
          <h1 className="orders-header__title">{getFilterTitle()}</h1>
          <p className="orders-header__subtitle">
            {getFilterDescription()}
          </p>
        </div>

        <div className="orders-header__stats">
          <div className="stat-card">
            <div className="stat-card__value">{orders.length}</div>
            <div className="stat-card__label">Total Orders</div>
          </div>
          <div className="stat-card">
            <div className="stat-card__value">{pendingCount}</div>
            <div className="stat-card__label">Pending</div>
          </div>
          <div className="stat-card">
            <div className="stat-card__value">{processingCount}</div>
            <div className="stat-card__label">Processing</div>
          </div>
          <div className="stat-card">
            <div className="stat-card__value">{completedCount}</div>
            <div className="stat-card__label">Completed</div>
          </div>
        </div>
      </div>
      <Link to ="/orders/add">
      <div className="buttonStyle">
        <button type="submit">Add Order</button>
      </div>
      </Link>

      {/* Filter Navigation */}
      <div className="orders-filter-nav">
        <button
          className={`filter-nav-button ${currentFilter === "all" ? "active" : ""}`}
          onClick={() => navigate("/orders")}
        >
          <span className="filter-nav-icon">🛒</span>
          All Orders
        </button>
        <button
          className={`filter-nav-button ${currentFilter === "pending" ? "active" : ""}`}
          onClick={() => navigate("/orders/pending")}
        >
          <span className="filter-nav-icon">⏳</span>
          Pending
        </button>
        <button
          className={`filter-nav-button ${currentFilter === "processing" ? "active" : ""}`}
          onClick={() => navigate("/orders/processing")}
        >
          <span className="filter-nav-icon">🔄</span>
          Processing
        </button>
        <button
          className={`filter-nav-button ${currentFilter === "completed" ? "active" : ""}`}
          onClick={() => navigate("/orders/completed")}
        >
          <span className="filter-nav-icon">✅</span>
          Completed
        </button>
      </div>

      {/* Controls Section */}
      <div className="orders-controls">
        <div className="orders-controls__search">
          <div className="search-input-container">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              placeholder="Search orders by customer name, email, or address..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="orders-controls__filters">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="control-select"
          >
            <option value="created_at">Sort by Date</option>
            <option value="customer_name">Sort by Customer</option>
            <option value="total_amount">Sort by Amount</option>
            <option value="status">Sort by Status</option>
          </select>
        </div>
      </div>

      {/* Orders Grid */}
      {filteredAndSortedOrders.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state__icon">📋</div>
          <h2 className="empty-state__title">No Orders Found</h2>
          <p className="empty-state__message">
            {searchTerm || currentFilter !== "all"
              ? "Try adjusting your search or filter criteria."
              : "No orders are available."}
          </p>
        </div>
      ) : (
        <div className="orders-grid">
          {filteredAndSortedOrders.map(order => {
            const statusInfo = getStatusInfo(order.status);
            const totalItems = getTotalItems(order.items);

            return (
              <div
                key={order.id}
                className="order-card"
                onClick={() => handleOrderClick(order)}
              >
                <div className="order-card__header">
                  <div className="order-card__customer">
                    <h3 className="order-card__customer-name">{order.customer_name}</h3>
                    <span className="order-card__order-id">Order #{order.id}</span>
                  </div>
                  <div className={`order-card__status order-card__status--${statusInfo.className}`}>
                    <span className="order-card__status-icon">{statusInfo.icon}</span>
                    <span className="order-card__status-text">{statusInfo.text}</span>
                  </div>
                </div>

                <div className="order-card__content">
                  <div className="order-card__email">
                    <span className="order-card__label">Email:</span>
                    <span className="order-card__value">{order.email}</span>
                  </div>

                  <div className="order-card__address">
                    <span className="order-card__label">Address:</span>
                    <span className="order-card__value">
                      {order.address.length > 50
                        ? `${order.address.substring(0, 50)}...`
                        : order.address}
                    </span>
                  </div>
                </div>

                <div className="order-card__details">
                  <div className="order-card__amount">
                    <span className="order-card__amount-label">Total Amount</span>
                    <span className="order-card__amount-value">
                      {formatCurrency(order.total_amount)}
                    </span>
                  </div>

                  <div className="order-card__items">
                    <span className="order-card__items-label">Items</span>
                    <span className="order-card__items-value">
                      {totalItems} {totalItems === 1 ? 'item' : 'items'}
                    </span>
                  </div>
                </div>

                <div className="order-card__footer">
                  <span className="order-card__date">
                    {formatDate(order.created_at)}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default Orders;