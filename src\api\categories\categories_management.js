import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "./categories_management.css";

function CategoriesManagement() {
  const [categories, setCategories] = useState([]);
  const [subcategories, setSubcategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [expandedCategories, setExpandedCategories] = useState(new Set());
  
  // Modal states
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showSubcategoryModal, setShowSubcategoryModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  // Form states
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingSubcategory, setEditingSubcategory] = useState(null);
  const [deleteTarget, setDeleteTarget] = useState(null);
  const [selectedCategoryForSub, setSelectedCategoryForSub] = useState(null);
  
  // Form data
  const [categoryForm, setCategoryForm] = useState({ name: '', slug: '', image: '' });
  const [subcategoryForm, setSubcategoryForm] = useState({ name: '', slug: '', category: '' });

  // Test API endpoints
  const testAPIEndpoints = async () => {
    const token = localStorage.getItem("access_token");
    console.log('Testing API endpoints...');

    // Test if endpoints support different methods
    try {
      const response = await fetch("https://ecommerce.techtrendo.com/api/categories/", {
        method: 'OPTIONS',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('Categories OPTIONS response:', response.status, response.headers);

      // Try to get allowed methods from headers
      const allowHeader = response.headers.get('Allow');
      console.log('Allowed methods for categories:', allowHeader);
    } catch (err) {
      console.log('Categories OPTIONS error:', err);
    }

    try {
      const response = await fetch("https://ecommerce.techtrendo.com/api/subcategories/", {
        method: 'OPTIONS',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('Subcategories OPTIONS response:', response.status, response.headers);

      // Try to get allowed methods from headers
      const allowHeader = response.headers.get('Allow');
      console.log('Allowed methods for subcategories:', allowHeader);
    } catch (err) {
      console.log('Subcategories OPTIONS error:', err);
    }

    // Test with a minimal POST request to see what error we get
    try {
      console.log('Testing minimal category creation...');
      const testResponse = await fetch("https://ecommerce.techtrendo.com/api/categories/", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name: 'Test Category', slug: 'test-category' })
      });

      console.log('Test POST response status:', testResponse.status);
      const responseText = await testResponse.text();
      console.log('Test POST response body:', responseText);

      if (testResponse.ok) {
        console.log('✅ POST is supported! The test category was created.');
        // Delete the test category if it was created
        try {
          const data = JSON.parse(responseText);
          if (data.id) {
            await fetch(`https://ecommerce.techtrendo.com/api/categories/${data.id}/`, {
              method: 'DELETE',
              headers: { 'Authorization': `Bearer ${token}` }
            });
            console.log('Test category deleted');
          }
        } catch (e) {
          console.log('Could not delete test category:', e);
        }
      }
    } catch (err) {
      console.log('Test POST error:', err);
    }
  };
  
  const navigate = useNavigate();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem("access_token");
      
      const [categoriesResponse, subcategoriesResponse] = await Promise.all([
        fetch("https://ecommerce.techtrendo.com/api/categories/", {
          headers: { ...(token ? { Authorization: `Bearer ${token}` } : {}) }
        }),
        fetch("https://ecommerce.techtrendo.com/api/subcategories/", {
          headers: { ...(token ? { Authorization: `Bearer ${token}` } : {}) }
        })
      ]);

      if (!categoriesResponse.ok || !subcategoriesResponse.ok) {
        throw new Error('Failed to fetch data');
      }

      const categoriesData = await categoriesResponse.json();
      const subcategoriesData = await subcategoriesResponse.json();
      
      setCategories(Array.isArray(categoriesData) ? categoriesData : categoriesData.results || []);
      setSubcategories(Array.isArray(subcategoriesData) ? subcategoriesData : subcategoriesData.results || []);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getSubcategoriesForCategory = (categoryId) => {
    return subcategories.filter(sub => sub.category === categoryId);
  };

  const toggleCategoryExpansion = (categoryId) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Category CRUD operations
  const handleCreateCategory = () => {
    setCategoryForm({ name: '', slug: '', image: '' });
    setEditingCategory(null);
    setShowCategoryModal(true);
  };

  const handleEditCategory = (category) => {
    setCategoryForm({ 
      name: category.name, 
      slug: category.slug, 
      image: category.image || '' 
    });
    setEditingCategory(category);
    setShowCategoryModal(true);
  };

  const handleDeleteCategory = (category) => {
    setDeleteTarget({ type: 'category', item: category });
    setShowDeleteModal(true);
  };

  // Subcategory CRUD operations
  const handleCreateSubcategory = (categoryId = null) => {
    setSubcategoryForm({ 
      name: '', 
      slug: '', 
      category: categoryId || '' 
    });
    setEditingSubcategory(null);
    setSelectedCategoryForSub(categoryId);
    setShowSubcategoryModal(true);
  };

  const handleEditSubcategory = (subcategory) => {
    setSubcategoryForm({
      name: subcategory.name,
      slug: subcategory.slug,
      category: subcategory.category
    });
    setEditingSubcategory(subcategory);
    setShowSubcategoryModal(true);
  };

  const handleDeleteSubcategory = (subcategory) => {
    setDeleteTarget({ type: 'subcategory', item: subcategory });
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <div className="management-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading categories management...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="management-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Error Loading Data</h2>
          <p>{error}</p>
          <button className="retry-button" onClick={fetchData}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // API operations
  const saveCategory = async () => {
    try {
      const token = localStorage.getItem("access_token");

      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      const method = editingCategory ? 'PUT' : 'POST';
      const url = editingCategory
        ? `https://ecommerce.techtrendo.com/api/categories/${editingCategory.id}/`
        : 'https://ecommerce.techtrendo.com/api/categories/';

      // Prepare the payload - only include non-empty fields
      const payload = {
        name: categoryForm.name,
        slug: categoryForm.slug
      };

      // Only include image if it's not empty
      if (categoryForm.image && categoryForm.image.trim()) {
        payload.image = categoryForm.image;
      }

      console.log('Saving category:', { method, url, data: payload, token: token ? 'present' : 'missing' });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Error response:', errorData);
        throw new Error(`Failed to save category (${response.status}): ${errorData}`);
      }

      const result = await response.json();
      console.log('Save successful:', result);

      setSuccess(`Category "${categoryForm.name}" ${editingCategory ? 'updated' : 'created'} successfully!`);
      setTimeout(() => setSuccess(null), 5000);

      await fetchData();
      setShowCategoryModal(false);
    } catch (err) {
      console.error('Save category error:', err);
      setError(err.message);
    }
  };

  const saveSubcategory = async () => {
    try {
      const token = localStorage.getItem("access_token");

      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      const method = editingSubcategory ? 'PUT' : 'POST';
      const url = editingSubcategory
        ? `https://ecommerce.techtrendo.com/api/subcategories/${editingSubcategory.id}/`
        : 'https://ecommerce.techtrendo.com/api/subcategories/';

      // Prepare the payload
      const payload = {
        name: subcategoryForm.name,
        slug: subcategoryForm.slug,
        category: subcategoryForm.category
      };

      console.log('Saving subcategory:', { method, url, data: payload, token: token ? 'present' : 'missing' });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Error response:', errorData);
        throw new Error(`Failed to save subcategory (${response.status}): ${errorData}`);
      }

      const result = await response.json();
      console.log('Save successful:', result);

      setSuccess(`Subcategory "${subcategoryForm.name}" ${editingSubcategory ? 'updated' : 'created'} successfully!`);
      setTimeout(() => setSuccess(null), 5000);

      await fetchData();
      setShowSubcategoryModal(false);
    } catch (err) {
      console.error('Save subcategory error:', err);
      setError(err.message);
    }
  };

  const confirmDelete = async () => {
    try {
      const token = localStorage.getItem("access_token");

      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      const { type, item } = deleteTarget;
      const url = type === 'category'
        ? `https://ecommerce.techtrendo.com/api/categories/${item.id}/`
        : `https://ecommerce.techtrendo.com/api/subcategories/${item.id}/`;

      console.log('Deleting:', { type, item: item.id, url, token: token ? 'present' : 'missing' });

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Delete response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Delete error response:', errorData);
        throw new Error(`Failed to delete ${type} (${response.status}): ${errorData}`);
      }

      console.log('Delete successful');

      setSuccess(`${deleteTarget.type} "${deleteTarget.item.name}" deleted successfully!`);
      setTimeout(() => setSuccess(null), 5000);

      await fetchData();
      setShowDeleteModal(false);
      setDeleteTarget(null);
    } catch (err) {
      console.error('Delete error:', err);
      setError(err.message);
    }
  };

  return (
    <div className="management-container">
      {/* Header */}
      <div className="management-header">
        <div className="management-header__content">
          <button
            className="back-button"
            onClick={() => navigate('/categories')}
          >
            ← Back to Categories
          </button>
          <h1 className="management-header__title">Categories Management</h1>
          <p className="management-header__subtitle">
            Manage categories and subcategories with full CRUD operations
          </p>
        </div>
        <div className="management-header__actions">
          <button
            className="action-button action-button--warning"
            onClick={testAPIEndpoints}
            style={{ fontSize: '0.75rem' }}
          >
            🔍 Test API
          </button>
          <button
            className="action-button action-button--primary"
            onClick={handleCreateCategory}
          >
            + Add Category
          </button>
          <button
            className="action-button action-button--secondary"
            onClick={() => handleCreateSubcategory()}
          >
            + Add Subcategory
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-banner">
          <div className="error-banner__content">
            <span className="error-banner__icon">⚠️</span>
            <span className="error-banner__message">{error}</span>
            <button
              className="error-banner__close"
              onClick={() => setError(null)}
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Success Display */}
      {success && (
        <div className="success-banner">
          <div className="success-banner__content">
            <span className="success-banner__icon">✅</span>
            <span className="success-banner__message">{success}</span>
            <button
              className="success-banner__close"
              onClick={() => setSuccess(null)}
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="management-stats">
        <div className="stat-card">
          <div className="stat-card__value">{categories.length}</div>
          <div className="stat-card__label">Categories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{subcategories.length}</div>
          <div className="stat-card__label">Subcategories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{expandedCategories.size}</div>
          <div className="stat-card__label">Expanded</div>
        </div>
      </div>

      {/* Categories List */}
      <div className="categories-management-list">
        {categories.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state__icon">🏷️</div>
            <h3>No Categories Found</h3>
            <p>Start by creating your first category</p>
            <button 
              className="action-button action-button--primary"
              onClick={handleCreateCategory}
            >
              Create Category
            </button>
          </div>
        ) : (
          categories.map(category => {
            const categorySubcategories = getSubcategoriesForCategory(category.id);
            const isExpanded = expandedCategories.has(category.id);
            
            return (
              <div key={category.id} className="category-management-item">
                <div className="category-item-header">
                  <div className="category-item-info">
                    <h3 className="category-item-name">{category.name}</h3>
                    <span className="category-item-slug">/{category.slug}</span>
                    <span className="category-item-count">
                      {categorySubcategories.length} subcategories
                    </span>
                  </div>
                  <div className="category-item-actions">
                    <button
                      className="icon-button"
                      onClick={() => toggleCategoryExpansion(category.id)}
                      title={isExpanded ? 'Collapse' : 'Expand'}
                    >
                      {isExpanded ? '▲' : '▼'}
                    </button>
                    <button
                      className="icon-button icon-button--success"
                      onClick={() => handleCreateSubcategory(category.id)}
                      title="Add Subcategory"
                    >
                      +
                    </button>
                    <button
                      className="icon-button icon-button--primary"
                      onClick={() => handleEditCategory(category)}
                      title="Edit Category"
                    >
                      ✏️
                    </button>
                    <button
                      className="icon-button icon-button--danger"
                      onClick={() => handleDeleteCategory(category)}
                      title="Delete Category"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
                
                {isExpanded && (
                  <div className="subcategories-section">
                    {categorySubcategories.length === 0 ? (
                      <div className="subcategories-empty">
                        <p>No subcategories in this category</p>
                        <button
                          className="action-button action-button--small"
                          onClick={() => handleCreateSubcategory(category.id)}
                        >
                          Add First Subcategory
                        </button>
                      </div>
                    ) : (
                      <div className="subcategories-list">
                        {categorySubcategories.map(subcategory => (
                          <div key={subcategory.id} className="subcategory-item">
                            <div className="subcategory-info">
                              <span className="subcategory-name">{subcategory.name}</span>
                              <span className="subcategory-slug">/{subcategory.slug}</span>
                            </div>
                            <div className="subcategory-actions">
                              <button
                                className="icon-button icon-button--primary"
                                onClick={() => handleEditSubcategory(subcategory)}
                                title="Edit Subcategory"
                              >
                                ✏️
                              </button>
                              <button
                                className="icon-button icon-button--danger"
                                onClick={() => handleDeleteSubcategory(subcategory)}
                                title="Delete Subcategory"
                              >
                                🗑️
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="modal-overlay" onClick={() => setShowCategoryModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{editingCategory ? 'Edit Category' : 'Create Category'}</h3>
              <button
                className="modal-close"
                onClick={() => setShowCategoryModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Name</label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={e => setCategoryForm({...categoryForm, name: e.target.value})}
                  placeholder="Category name"
                />
              </div>
              <div className="form-group">
                <label>Slug</label>
                <input
                  type="text"
                  value={categoryForm.slug}
                  onChange={e => setCategoryForm({...categoryForm, slug: e.target.value})}
                  placeholder="category-slug"
                />
              </div>
              <div className="form-group">
                <label>Image URL (optional)</label>
                <input
                  type="url"
                  value={categoryForm.image}
                  onChange={e => setCategoryForm({...categoryForm, image: e.target.value})}
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="action-button action-button--secondary"
                onClick={() => setShowCategoryModal(false)}
              >
                Cancel
              </button>
              <button
                className="action-button action-button--primary"
                onClick={saveCategory}
                disabled={!categoryForm.name || !categoryForm.slug}
              >
                {editingCategory ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Subcategory Modal */}
      {showSubcategoryModal && (
        <div className="modal-overlay" onClick={() => setShowSubcategoryModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{editingSubcategory ? 'Edit Subcategory' : 'Create Subcategory'}</h3>
              <button
                className="modal-close"
                onClick={() => setShowSubcategoryModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Name</label>
                <input
                  type="text"
                  value={subcategoryForm.name}
                  onChange={e => setSubcategoryForm({...subcategoryForm, name: e.target.value})}
                  placeholder="Subcategory name"
                />
              </div>
              <div className="form-group">
                <label>Slug</label>
                <input
                  type="text"
                  value={subcategoryForm.slug}
                  onChange={e => setSubcategoryForm({...subcategoryForm, slug: e.target.value})}
                  placeholder="subcategory-slug"
                />
              </div>
              <div className="form-group">
                <label>Category</label>
                <select
                  value={subcategoryForm.category}
                  onChange={e => setSubcategoryForm({...subcategoryForm, category: parseInt(e.target.value)})}
                >
                  <option value="">Select a category</option>
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="action-button action-button--secondary"
                onClick={() => setShowSubcategoryModal(false)}
              >
                Cancel
              </button>
              <button
                className="action-button action-button--primary"
                onClick={saveSubcategory}
                disabled={!subcategoryForm.name || !subcategoryForm.slug || !subcategoryForm.category}
              >
                {editingSubcategory ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && deleteTarget && (
        <div className="modal-overlay" onClick={() => setShowDeleteModal(false)}>
          <div className="modal-content modal-content--danger" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Confirm Delete</h3>
              <button
                className="modal-close"
                onClick={() => setShowDeleteModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <p>
                Are you sure you want to delete the {deleteTarget.type}
                <strong> "{deleteTarget.item.name}"</strong>?
              </p>
              {deleteTarget.type === 'category' && (
                <p className="warning-text">
                  ⚠️ This will also delete all subcategories in this category!
                </p>
              )}
            </div>
            <div className="modal-footer">
              <button
                className="action-button action-button--secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                Cancel
              </button>
              <button
                className="action-button action-button--danger"
                onClick={confirmDelete}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default CategoriesManagement;
