import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "./categories_management.css";

function CategoriesManagement() {
  const [categories, setCategories] = useState([]);
  const [subcategories, setSubcategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedCategories, setExpandedCategories] = useState(new Set());
  
  // Modal states
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showSubcategoryModal, setShowSubcategoryModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  // Form states
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingSubcategory, setEditingSubcategory] = useState(null);
  const [deleteTarget, setDeleteTarget] = useState(null);
  const [selectedCategoryForSub, setSelectedCategoryForSub] = useState(null);
  
  // Form data
  const [categoryForm, setCategoryForm] = useState({ name: '', slug: '', image: '' });
  const [subcategoryForm, setSubcategoryForm] = useState({ name: '', slug: '', category: '' });
  
  const navigate = useNavigate();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem("access_token");
      
      const [categoriesResponse, subcategoriesResponse] = await Promise.all([
        fetch("https://ecommerce.techtrendo.com/api/categories/", {
          headers: { ...(token ? { Authorization: `Bearer ${token}` } : {}) }
        }),
        fetch("https://ecommerce.techtrendo.com/api/subcategories/", {
          headers: { ...(token ? { Authorization: `Bearer ${token}` } : {}) }
        })
      ]);

      if (!categoriesResponse.ok || !subcategoriesResponse.ok) {
        throw new Error('Failed to fetch data');
      }

      const categoriesData = await categoriesResponse.json();
      const subcategoriesData = await subcategoriesResponse.json();
      
      setCategories(Array.isArray(categoriesData) ? categoriesData : categoriesData.results || []);
      setSubcategories(Array.isArray(subcategoriesData) ? subcategoriesData : subcategoriesData.results || []);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getSubcategoriesForCategory = (categoryId) => {
    return subcategories.filter(sub => sub.category === categoryId);
  };

  const toggleCategoryExpansion = (categoryId) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Category CRUD operations
  const handleCreateCategory = () => {
    setCategoryForm({ name: '', slug: '', image: '' });
    setEditingCategory(null);
    setShowCategoryModal(true);
  };

  const handleEditCategory = (category) => {
    setCategoryForm({ 
      name: category.name, 
      slug: category.slug, 
      image: category.image || '' 
    });
    setEditingCategory(category);
    setShowCategoryModal(true);
  };

  const handleDeleteCategory = (category) => {
    setDeleteTarget({ type: 'category', item: category });
    setShowDeleteModal(true);
  };

  // Subcategory CRUD operations
  const handleCreateSubcategory = (categoryId = null) => {
    setSubcategoryForm({ 
      name: '', 
      slug: '', 
      category: categoryId || '' 
    });
    setEditingSubcategory(null);
    setSelectedCategoryForSub(categoryId);
    setShowSubcategoryModal(true);
  };

  const handleEditSubcategory = (subcategory) => {
    setSubcategoryForm({
      name: subcategory.name,
      slug: subcategory.slug,
      category: subcategory.category
    });
    setEditingSubcategory(subcategory);
    setShowSubcategoryModal(true);
  };

  const handleDeleteSubcategory = (subcategory) => {
    setDeleteTarget({ type: 'subcategory', item: subcategory });
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <div className="management-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading categories management...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="management-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Error Loading Data</h2>
          <p>{error}</p>
          <button className="retry-button" onClick={fetchData}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // API operations
  const saveCategory = async () => {
    try {
      const token = localStorage.getItem("access_token");
      const method = editingCategory ? 'PUT' : 'POST';
      const url = editingCategory
        ? `https://ecommerce.techtrendo.com/api/categories/${editingCategory.id}/`
        : 'https://ecommerce.techtrendo.com/api/categories/';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        },
        body: JSON.stringify(categoryForm)
      });

      if (!response.ok) throw new Error('Failed to save category');

      await fetchData();
      setShowCategoryModal(false);
    } catch (err) {
      setError(err.message);
    }
  };

  const saveSubcategory = async () => {
    try {
      const token = localStorage.getItem("access_token");
      const method = editingSubcategory ? 'PUT' : 'POST';
      const url = editingSubcategory
        ? `https://ecommerce.techtrendo.com/api/subcategories/${editingSubcategory.id}/`
        : 'https://ecommerce.techtrendo.com/api/subcategories/';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        },
        body: JSON.stringify(subcategoryForm)
      });

      if (!response.ok) throw new Error('Failed to save subcategory');

      await fetchData();
      setShowSubcategoryModal(false);
    } catch (err) {
      setError(err.message);
    }
  };

  const confirmDelete = async () => {
    try {
      const token = localStorage.getItem("access_token");
      const { type, item } = deleteTarget;
      const url = type === 'category'
        ? `https://ecommerce.techtrendo.com/api/categories/${item.id}/`
        : `https://ecommerce.techtrendo.com/api/subcategories/${item.id}/`;

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      });

      if (!response.ok) throw new Error(`Failed to delete ${type}`);

      await fetchData();
      setShowDeleteModal(false);
      setDeleteTarget(null);
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div className="management-container">
      {/* Header */}
      <div className="management-header">
        <div className="management-header__content">
          <button
            className="back-button"
            onClick={() => navigate('/categories')}
          >
            ← Back to Categories
          </button>
          <h1 className="management-header__title">Categories Management</h1>
          <p className="management-header__subtitle">
            Manage categories and subcategories with full CRUD operations
          </p>
        </div>
        <div className="management-header__actions">
          <button
            className="action-button action-button--primary"
            onClick={handleCreateCategory}
          >
            + Add Category
          </button>
          <button
            className="action-button action-button--secondary"
            onClick={() => handleCreateSubcategory()}
          >
            + Add Subcategory
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="management-stats">
        <div className="stat-card">
          <div className="stat-card__value">{categories.length}</div>
          <div className="stat-card__label">Categories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{subcategories.length}</div>
          <div className="stat-card__label">Subcategories</div>
        </div>
        <div className="stat-card">
          <div className="stat-card__value">{expandedCategories.size}</div>
          <div className="stat-card__label">Expanded</div>
        </div>
      </div>

      {/* Categories List */}
      <div className="categories-management-list">
        {categories.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state__icon">🏷️</div>
            <h3>No Categories Found</h3>
            <p>Start by creating your first category</p>
            <button 
              className="action-button action-button--primary"
              onClick={handleCreateCategory}
            >
              Create Category
            </button>
          </div>
        ) : (
          categories.map(category => {
            const categorySubcategories = getSubcategoriesForCategory(category.id);
            const isExpanded = expandedCategories.has(category.id);
            
            return (
              <div key={category.id} className="category-management-item">
                <div className="category-item-header">
                  <div className="category-item-info">
                    <h3 className="category-item-name">{category.name}</h3>
                    <span className="category-item-slug">/{category.slug}</span>
                    <span className="category-item-count">
                      {categorySubcategories.length} subcategories
                    </span>
                  </div>
                  <div className="category-item-actions">
                    <button
                      className="icon-button"
                      onClick={() => toggleCategoryExpansion(category.id)}
                      title={isExpanded ? 'Collapse' : 'Expand'}
                    >
                      {isExpanded ? '▲' : '▼'}
                    </button>
                    <button
                      className="icon-button icon-button--success"
                      onClick={() => handleCreateSubcategory(category.id)}
                      title="Add Subcategory"
                    >
                      +
                    </button>
                    <button
                      className="icon-button icon-button--primary"
                      onClick={() => handleEditCategory(category)}
                      title="Edit Category"
                    >
                      ✏️
                    </button>
                    <button
                      className="icon-button icon-button--danger"
                      onClick={() => handleDeleteCategory(category)}
                      title="Delete Category"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
                
                {isExpanded && (
                  <div className="subcategories-section">
                    {categorySubcategories.length === 0 ? (
                      <div className="subcategories-empty">
                        <p>No subcategories in this category</p>
                        <button
                          className="action-button action-button--small"
                          onClick={() => handleCreateSubcategory(category.id)}
                        >
                          Add First Subcategory
                        </button>
                      </div>
                    ) : (
                      <div className="subcategories-list">
                        {categorySubcategories.map(subcategory => (
                          <div key={subcategory.id} className="subcategory-item">
                            <div className="subcategory-info">
                              <span className="subcategory-name">{subcategory.name}</span>
                              <span className="subcategory-slug">/{subcategory.slug}</span>
                            </div>
                            <div className="subcategory-actions">
                              <button
                                className="icon-button icon-button--primary"
                                onClick={() => handleEditSubcategory(subcategory)}
                                title="Edit Subcategory"
                              >
                                ✏️
                              </button>
                              <button
                                className="icon-button icon-button--danger"
                                onClick={() => handleDeleteSubcategory(subcategory)}
                                title="Delete Subcategory"
                              >
                                🗑️
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="modal-overlay" onClick={() => setShowCategoryModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{editingCategory ? 'Edit Category' : 'Create Category'}</h3>
              <button
                className="modal-close"
                onClick={() => setShowCategoryModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Name</label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={e => setCategoryForm({...categoryForm, name: e.target.value})}
                  placeholder="Category name"
                />
              </div>
              <div className="form-group">
                <label>Slug</label>
                <input
                  type="text"
                  value={categoryForm.slug}
                  onChange={e => setCategoryForm({...categoryForm, slug: e.target.value})}
                  placeholder="category-slug"
                />
              </div>
              <div className="form-group">
                <label>Image URL (optional)</label>
                <input
                  type="url"
                  value={categoryForm.image}
                  onChange={e => setCategoryForm({...categoryForm, image: e.target.value})}
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="action-button action-button--secondary"
                onClick={() => setShowCategoryModal(false)}
              >
                Cancel
              </button>
              <button
                className="action-button action-button--primary"
                onClick={saveCategory}
                disabled={!categoryForm.name || !categoryForm.slug}
              >
                {editingCategory ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Subcategory Modal */}
      {showSubcategoryModal && (
        <div className="modal-overlay" onClick={() => setShowSubcategoryModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{editingSubcategory ? 'Edit Subcategory' : 'Create Subcategory'}</h3>
              <button
                className="modal-close"
                onClick={() => setShowSubcategoryModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Name</label>
                <input
                  type="text"
                  value={subcategoryForm.name}
                  onChange={e => setSubcategoryForm({...subcategoryForm, name: e.target.value})}
                  placeholder="Subcategory name"
                />
              </div>
              <div className="form-group">
                <label>Slug</label>
                <input
                  type="text"
                  value={subcategoryForm.slug}
                  onChange={e => setSubcategoryForm({...subcategoryForm, slug: e.target.value})}
                  placeholder="subcategory-slug"
                />
              </div>
              <div className="form-group">
                <label>Category</label>
                <select
                  value={subcategoryForm.category}
                  onChange={e => setSubcategoryForm({...subcategoryForm, category: parseInt(e.target.value)})}
                >
                  <option value="">Select a category</option>
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="action-button action-button--secondary"
                onClick={() => setShowSubcategoryModal(false)}
              >
                Cancel
              </button>
              <button
                className="action-button action-button--primary"
                onClick={saveSubcategory}
                disabled={!subcategoryForm.name || !subcategoryForm.slug || !subcategoryForm.category}
              >
                {editingSubcategory ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && deleteTarget && (
        <div className="modal-overlay" onClick={() => setShowDeleteModal(false)}>
          <div className="modal-content modal-content--danger" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Confirm Delete</h3>
              <button
                className="modal-close"
                onClick={() => setShowDeleteModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <p>
                Are you sure you want to delete the {deleteTarget.type}
                <strong> "{deleteTarget.item.name}"</strong>?
              </p>
              {deleteTarget.type === 'category' && (
                <p className="warning-text">
                  ⚠️ This will also delete all subcategories in this category!
                </p>
              )}
            </div>
            <div className="modal-footer">
              <button
                className="action-button action-button--secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                Cancel
              </button>
              <button
                className="action-button action-button--danger"
                onClick={confirmDelete}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default CategoriesManagement;
