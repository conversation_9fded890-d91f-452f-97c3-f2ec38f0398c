import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import './Header.css';


function Header() {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [showUserPopup, setShowUserPopup] = useState(false);
  const userInfoRef = useRef(null);

  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  // Close popup when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (userInfoRef.current && !userInfoRef.current.contains(event.target)) {
        setShowUserPopup(false);
      }
    }
    if (showUserPopup) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserPopup]);

  return (
    <>
      {/* Mobile toggle button */}
      <button
        className="sidebar__mobile-toggle"
        onClick={toggleMobile}
        aria-label="Toggle navigation menu"
      >
        ☰
      </button>

      {/* Mobile overlay */}
      <div
        className={`sidebar-overlay ${isMobileOpen ? 'sidebar-overlay--visible' : ''}`}
        onClick={toggleMobile}
      />

      {/* Sidebar */}
      <aside className={`sidebar ${isMobileOpen ? 'sidebar--mobile-open' : ''}`}>
        <div className="sidebar__container">
          {/* Brand/Logo Section */}
          <div className="sidebar__brand">
            <div className="sidebar__brand-logo">
              IM
            </div>
            <span className="sidebar__brand-text">Inventory Manager</span>
          </div>

          {/* Main Navigation */}
          <nav className="sidebar__nav">
            {/* Main Section */}
            <div className="sidebar__nav-section">
              <h3 className="sidebar__nav-title">Main</h3>
              <div className="sidebar__links">
                <Link to="/" className="sidebar__link sidebar__link--active">
                  <span className="sidebar__link-icon">🏠</span>
                  <span className="sidebar__link-text">Dashboard</span>
                </Link>
                <Link to="/products" className="sidebar__link">
                  <span className="sidebar__link-icon">📦</span>
                  <span className="sidebar__link-text">Products</span>
                </Link>
                <Link to="/categories" className="sidebar__link">
                  <span className="sidebar__link-icon">🏷️</span>
                  <span className="sidebar__link-text">Categories</span>
                </Link>
              </div>
            </div>

            {/* Orders Section */}
            <div className="sidebar__nav-section">
              <h3 className="sidebar__nav-title">Orders</h3>
              <div className="sidebar__links">
                <Link to="/orders" className="sidebar__link">
                  <span className="sidebar__link-icon">🛒</span>
                  <span className="sidebar__link-text">All Orders</span>
                </Link>
                <Link to="/orders/pending" className="sidebar__link">
                  <span className="sidebar__link-icon">⏳</span>
                  <span className="sidebar__link-text">Pending</span>
                </Link>
                <Link to="/orders/processing" className="sidebar__link">
                  <span className="sidebar__link-icon">🔄</span>
                  <span className="sidebar__link-text">Processing</span>
                </Link>
                <Link to="/orders/completed" className="sidebar__link">
                  <span className="sidebar__link-icon">✅</span>
                  <span className="sidebar__link-text">Completed</span>
                </Link>
              </div>
            </div>

          </nav>

          {/* Footer/User Section */}
          <div className="sidebar__footer">

            <div className="sidebar__user" ref={userInfoRef}>
              <div className="sidebar__user-avatar">
                JD
              </div>
              <div
                className="sidebar__user-info"
                style={{ cursor: 'pointer' }}
                onClick={() => setShowUserPopup((v) => !v)}
              >
                <div className="sidebar__user-name">John Doe</div>
                <div className="sidebar__user-role">Administrator</div>
              </div>
              {showUserPopup && (
                <div className="sidebar__user-popup" style={{
                  position: 'absolute',
                  bottom: '60px',
                  left: '0',
                  background: '#fff',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                  borderRadius: '8px',
                  zIndex: 1000,
                  minWidth: '120px',
                  padding: '8px 0',
                }}>
                  <Link to="/login" className="sidebar__user-popup-item">
                  <button
                    className="sidebar__user-popup-item"
                    style={{
                      display: 'block',
                      width: '100%',
                      background: 'none',
                      border: 'none',
                      textAlign: 'left',
                      padding: '8px 16px',
                      cursor: 'pointer',
                    }}
                    onClick={() => { /* Add logout logic here */
                       setShowUserPopup(false); }}
                  >Log In</button>
                  </Link>

                  <Link to="/logout" >
                  <button
                    className="sidebar__user-popup-item"
                    style={{
                      display: 'block',
                      width: '100%',
                      background: 'none',
                      border: 'none',
                      textAlign: 'left',
                      padding: '8px 16px',
                      cursor: 'pointer',
                    }}
                    onClick={() => { setShowUserPopup(false); }}
                  >Logout</button></Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}

export default Header
