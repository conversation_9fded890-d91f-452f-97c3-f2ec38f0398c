
import React, { useState, useEffect } from 'react';
import './order_add.css';

function OrderAdd() {
  const [form, setForm] = useState({
    customer_name: '',
    email: '',
    address: '',
    status: '',
    total_amount: '',
    items: [
      { product: '', variation: '', quantity: '', price: '' }
    ]
  });
  const [products, setProducts] = useState([]);
  const [variations, setVariations] = useState([]);

  useEffect(() => {
    const token = localStorage.getItem('access_token');
    // Fetch products
    fetch('https://ecommerce.techtrendo.com/api/products/', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) setProducts(data);
        else if (data && Array.isArray(data.results)) setProducts(data.results);
        else setProducts([]);
      })
      .catch(() => setProducts([]));
    // Fetch variations
    fetch('https://ecommerce.techtrendo.com/api/variations/', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) setVariations(data);
        else if (data && Array.isArray(data.results)) setVariations(data.results);
        else setVariations([]);
      })
      .catch(() => setVariations([]));
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });
  };

  const handleItemChange = (idx, e) => {
    const { name, value } = e.target;
    const newItems = form.items.map((item, i) =>
      i === idx ? { ...item, [name]: value } : item
    );
    setForm({ ...form, items: newItems });
  };

  const addItem = () => {
    setForm({
      ...form,
      items: [...form.items, { product: '', variation: '', quantity: '', price: '' }]
    });
  };

  const removeItem = (idx) => {
    setForm({
      ...form,
      items: form.items.filter((_, i) => i !== idx)
    });
  };
  const token = localStorage.getItem('access_token')
  const handleSubmit = async(e) => {
    e.preventDefault();
    
    try {

      const response = await fetch("https://ecommerce.techtrendo.com/api/orders/",{
        method: "POST",
        headers:{'Content-Type': 'application/json' ,
                  'Authorization': `Bearer ${token}`
        },     
        body: JSON.stringify(form)
      });
      const data = await response.json();
      console.log(data);
    }
    catch(err){
      console.log("error")
    }
  };

  return (
    <div className="order-add-container">
      <div className="order-add-title">Add New Order</div>
      <form className="order-add-form" onSubmit={handleSubmit} autoComplete="off">
        <div className="order-add-form-row">
          <label className="order-add-label">Customer Name*</label>
          <input className="order-add-input" type="text" name="customer_name" maxLength={100} minLength={1} required value={form.customer_name} onChange={handleChange} />
        </div>
        <div className="order-add-form-row">
          <label className="order-add-label">Email*</label>
          <input className="order-add-input" type="email" name="email" maxLength={254} minLength={1} required value={form.email} onChange={handleChange} />
        </div>
        <div className="order-add-form-row">
          <label className="order-add-label">Address*</label>
          <input className="order-add-input" type="text" name="address" minLength={1} required value={form.address} onChange={handleChange} />
        </div>
        <div className="order-add-form-row">
          <label className="order-add-label">Status</label>
          <select className="order-add-select" name="status" value={form.status} onChange={handleChange}>
            <option value="">Select status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
          </select>
        </div>
        <div className="order-add-form-row">
          <label className="order-add-label">Total Amount</label>
          <input className="order-add-input" type="number" name="total_amount" step="0.01" value={form.total_amount} onChange={handleChange} />
        </div>
        <fieldset className="order-add-items order-add-form-row-full">
          <legend className="order-add-label">Order Items*</legend>
          {form.items.map((item, idx) => (
            <div className="order-add-item-row" key={idx}>
              <select className="order-add-select" name="product" required value={item.product} onChange={e => handleItemChange(idx, e)}>
                <option value="">Select Product</option>
                {(Array.isArray(products) ? products : []).map(product => (
                  <option key={product.id} value={product.id}>{product.name || product.title || product.id}</option>
                ))}
              </select>
              <select className="order-add-select" name="variation" value={item.variation} onChange={e => handleItemChange(idx, e)}>
                <option value="">Select Variation</option>
                {(Array.isArray(variations) ? variations : []).map(variation => (
                  <option key={variation.id} value={variation.id}>{variation.name || variation.title || variation.id}</option>
                ))}
              </select>
              <input className="order-add-input" type="number" name="quantity" placeholder="Quantity" required value={item.quantity} onChange={e => handleItemChange(idx, e)} />
              <input className="order-add-input" type="number" name="price" placeholder="Price" step="0.01" required value={item.price} onChange={e => handleItemChange(idx, e)} />
              <button className="order-add-remove-btn" type="button" onClick={() => removeItem(idx)} disabled={form.items.length === 1}>Remove</button>
            </div>
          ))}
          <button className="order-add-add-btn" type="button" onClick={addItem}>Add Item</button>
        </fieldset>
        <div className="order-add-form-row order-add-form-row-full" style={{textAlign: 'center'}}>
          <button className="order-add-submit-btn" type="submit">Add Order</button>
        </div>
      </form>
    </div>
  );
}

export default OrderAdd
