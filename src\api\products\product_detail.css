/* Product Detail - Professional Inventory Management Interface */

/* Use the same CSS variables as products list for consistency */
:root {
  --products-primary-blue: #2563eb;
  --products-secondary-blue: #3b82f6;
  --products-light-blue: #dbeafe;
  --products-success-green: #10b981;
  --products-warning-yellow: #f59e0b;
  --products-danger-red: #ef4444;
  --products-text-primary: #1e293b;
  --products-text-secondary: #64748b;
  --products-text-muted: #94a3b8;
  --products-bg-primary: #ffffff;
  --products-bg-secondary: #f8fafc;
  --products-bg-tertiary: #f1f5f9;
  --products-border: #e2e8f0;
  --products-border-light: #f1f5f9;
  --products-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --products-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --products-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --products-transition: all 0.2s ease-in-out;
  --products-border-radius: 0.5rem;
  --products-border-radius-lg: 0.75rem;
}

/* Main Container */
.product-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: var(--products-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--products-border);
  border-top: 3px solid var(--products-primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-container h2 {
  color: var(--products-text-primary);
  margin-bottom: 0.5rem;
}

.back-button {
  background-color: var(--products-primary-blue);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--products-border-radius);
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  transition: var(--products-transition);
}

.back-button:hover {
  background-color: var(--products-secondary-blue);
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--products-border);
}

.breadcrumb-link {
  background: none;
  border: none;
  color: var(--products-primary-blue);
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: none;
  transition: var(--products-transition);
}

.breadcrumb-link:hover {
  color: var(--products-secondary-blue);
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: var(--products-text-muted);
}

.breadcrumb-current {
  color: var(--products-text-secondary);
  font-size: 0.875rem;
}

/* Product Detail Layout */
.product-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

/* Product Images */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.main-image-container {
  position: relative;
  background-color: var(--products-bg-secondary);
  border-radius: var(--products-border-radius-lg);
  overflow: hidden;
  aspect-ratio: 1;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: var(--products-warning-yellow);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--products-border-radius);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.thumbnail-images {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
}

.thumbnail-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--products-border-radius);
  border: 2px solid var(--products-border);
  cursor: pointer;
  transition: var(--products-transition);
  flex-shrink: 0;
}

.thumbnail-image:hover {
  border-color: var(--products-primary-blue);
}

/* Product Information */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-header {
  border-bottom: 1px solid var(--products-border);
  padding-bottom: 1.5rem;
}

.product-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--products-text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.product-sku {
  font-size: 0.875rem;
  color: var(--products-text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.75rem;
}

.stock-status {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: var(--products-border-radius);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.stock-status--in-stock {
  background-color: var(--products-success-green);
  color: white;
}

.stock-status--low-stock {
  background-color: var(--products-warning-yellow);
  color: white;
}

.stock-status--out-of-stock {
  background-color: var(--products-danger-red);
  color: white;
}

.stock-status--unavailable {
  background-color: var(--products-text-muted);
  color: white;
}

/* Product Pricing */
.product-pricing {
  padding: 1.5rem;
  background-color: var(--products-bg-secondary);
  border-radius: var(--products-border-radius-lg);
  border: 1px solid var(--products-border);
}

.price-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.price {
  font-weight: 600;
}

.price--current {
  font-size: 2rem;
  color: var(--products-primary-blue);
}

.price--original {
  font-size: 1.25rem;
  color: var(--products-text-muted);
  text-decoration: line-through;
}

.price--savings {
  font-size: 0.875rem;
  color: var(--products-success-green);
  background-color: rgba(16, 185, 129, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--products-border-radius);
}

/* Product Description */
.product-description h3 {
  color: var(--products-text-primary);
  margin: 0 0 0.75rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.product-description p {
  color: var(--products-text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Product Details Grid */
.product-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--products-bg-secondary);
  border-radius: var(--products-border-radius-lg);
  border: 1px solid var(--products-border);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.75rem;
  color: var(--products-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-weight: 600;
}

.detail-value {
  color: var(--products-text-primary);
  font-weight: 500;
}

.detail-value.low-stock {
  color: var(--products-danger-red);
  font-weight: 600;
}

/* Product Tags */
.product-tags h4 {
  color: var(--products-text-primary);
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background-color: var(--products-light-blue);
  color: var(--products-primary-blue);
  padding: 0.25rem 0.75rem;
  border-radius: var(--products-border-radius);
  font-size: 0.75rem;
  font-weight: 500;
}

/* Product Variations */
.product-variations h4 {
  color: var(--products-text-primary);
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.product-variations p {
  color: var(--products-text-secondary);
  margin: 0;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--products-border);
}

.action-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--products-border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--products-transition);
  border: 1px solid;
  flex: 1;
}

.action-button--primary {
  background-color: var(--products-primary-blue);
  color: white;
  border-color: var(--products-primary-blue);
}

.action-button--primary:hover {
  background-color: var(--products-secondary-blue);
  border-color: var(--products-secondary-blue);
}

.action-button--secondary {
  background-color: transparent;
  color: var(--products-primary-blue);
  border-color: var(--products-primary-blue);
}

.action-button--secondary:hover {
  background-color: var(--products-light-blue);
}

/* SEO Information */
.seo-info {
  background-color: var(--products-bg-secondary);
  border: 1px solid var(--products-border);
  border-radius: var(--products-border-radius-lg);
  padding: 1.5rem;
  margin-top: 2rem;
}

.seo-info h3 {
  color: var(--products-text-primary);
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.seo-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.seo-item:last-child {
  margin-bottom: 0;
}

.seo-label {
  font-size: 0.875rem;
  color: var(--products-text-muted);
  font-weight: 600;
}

.seo-value {
  color: var(--products-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-detail {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .product-title {
    font-size: 1.5rem;
  }

  .price--current {
    font-size: 1.5rem;
  }

  .product-details-grid {
    grid-template-columns: 1fr;
  }

  .product-actions {
    flex-direction: column;
  }

  .action-button {
    flex: none;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    font-size: 0.75rem;
  }

  .thumbnail-images {
    justify-content: center;
  }

  .price-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
