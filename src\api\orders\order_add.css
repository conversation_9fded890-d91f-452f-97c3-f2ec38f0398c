/* Order Add - Consistent with Orders UI */

:root {
  --orders-primary-blue: #2563eb;
  --orders-secondary-blue: #3b82f6;
  --orders-light-blue: #dbeafe;
  --orders-success-green: #10b981;
  --orders-warning-yellow: #f59e0b;
  --orders-danger-red: #ef4444;
  --orders-processing-purple: #8b5cf6;
  --orders-text-primary: #1e293b;
  --orders-text-secondary: #64748b;
  --orders-text-muted: #94a3b8;
  --orders-bg-primary: #ffffff;
  --orders-bg-secondary: #f8fafc;
  --orders-bg-tertiary: #f1f5f9;
  --orders-border: #e2e8f0;
  --orders-border-light: #f1f5f9;
  --orders-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --orders-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --orders-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --orders-transition: all 0.2s ease-in-out;
  --orders-border-radius: 0.5rem;
  --orders-border-radius-lg: 0.75rem;
}

.order-add-container {
  max-width: 600px;
  margin: 3rem auto;
  background: var(--orders-bg-primary);
  border-radius: var(--orders-border-radius);
  box-shadow: var(--orders-shadow);
  padding: 2.5rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-add-title {
  color: var(--orders-primary-blue);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  letter-spacing: -1px;
}

.order-add-form {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem 2rem;
  align-items: end;
}

.order-add-form .order-add-form-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  grid-column: span 1;
}

.order-add-form .order-add-form-row-full {
  grid-column: 1 / -1;
}

.order-add-label {
  color: var(--orders-text-primary);
  font-weight: 500;
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.order-add-input, .order-add-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius);
  font-size: 1rem;
  color: var(--orders-text-primary);
  background: var(--orders-bg-secondary);
  transition: var(--orders-transition);
  width: 100%;
  box-sizing: border-box;
}

.order-add-input:focus, .order-add-select:focus {
  outline: none;
  border-color: var(--orders-primary-blue);
  background: var(--orders-bg-tertiary);
}

.order-add-items {
  margin-top: 1.5rem;
  background: var(--orders-bg-secondary);
  border-radius: var(--orders-border-radius);
  padding: 1rem;
  box-shadow: var(--orders-shadow);
}

.order-add-item-row {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 1rem;
}

.order-add-item-row:last-child {
  margin-bottom: 0;
}

.order-add-remove-btn {
  background: var(--orders-danger-red);
  color: #fff;
  border: none;
  border-radius: var(--orders-border-radius);
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: var(--orders-transition);
}

.order-add-remove-btn:disabled {
  background: var(--orders-border);
  color: var(--orders-text-muted);
  cursor: not-allowed;
}

.order-add-add-btn {
  background: var(--orders-primary-blue);
  color: #fff;
  border: none;
  border-radius: var(--orders-border-radius);
  padding: 0.5rem 1.5rem;
  margin-top: 0.5rem;
  cursor: pointer;
  transition: var(--orders-transition);
}

.order-add-submit-btn {
  background: linear-gradient(90deg, var(--orders-primary-blue), var(--orders-secondary-blue));
  color: #fff;
  border: none;
  border-radius: var(--orders-border-radius);
  padding: 0.85rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 2.5rem;
  cursor: pointer;
  transition: var(--orders-transition);
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
  letter-spacing: 0.5px;
}

.order-add-submit-btn:hover, .order-add-add-btn:hover, .order-add-remove-btn:hover {
  filter: brightness(0.95);
}
