/* Order Detail - Professional Inventory Management Interface */

/* Use the same CSS variables as orders list for consistency */
:root {
  --orders-primary-blue: #2563eb;
  --orders-secondary-blue: #3b82f6;
  --orders-light-blue: #dbeafe;
  --orders-success-green: #10b981;
  --orders-warning-yellow: #f59e0b;
  --orders-danger-red: #ef4444;
  --orders-processing-purple: #8b5cf6;
  --orders-text-primary: #1e293b;
  --orders-text-secondary: #64748b;
  --orders-text-muted: #94a3b8;
  --orders-bg-primary: #ffffff;
  --orders-bg-secondary: #f8fafc;
  --orders-bg-tertiary: #f1f5f9;
  --orders-border: #e2e8f0;
  --orders-border-light: #f1f5f9;
  --orders-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --orders-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --orders-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --orders-transition: all 0.2s ease-in-out;
  --orders-border-radius: 0.5rem;
  --orders-border-radius-lg: 0.75rem;
}

/* Main Container */
.order-detail-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: var(--orders-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--orders-border);
  border-top: 3px solid var(--orders-primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-container h2 {
  color: var(--orders-text-primary);
  margin-bottom: 0.5rem;
}

.back-button {
  background-color: var(--orders-primary-blue);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
  transition: var(--orders-transition);
}

.back-button:hover {
  background-color: var(--orders-secondary-blue);
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--orders-border);
}

.breadcrumb-link {
  background: none;
  border: none;
  color: var(--orders-primary-blue);
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: none;
  transition: var(--orders-transition);
}

.breadcrumb-link:hover {
  color: var(--orders-secondary-blue);
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: var(--orders-text-muted);
}

.breadcrumb-current {
  color: var(--orders-text-secondary);
  font-size: 0.875rem;
}

/* Order Detail Layout */
.order-detail {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Order Header */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  background-color: var(--orders-bg-secondary);
  border-radius: var(--orders-border-radius-lg);
  border: 1px solid var(--orders-border);
}

.order-header__info {
  flex: 1;
}

.order-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--orders-text-primary);
  margin: 0 0 0.5rem 0;
}

.order-date {
  color: var(--orders-text-secondary);
  font-size: 0.875rem;
}

.order-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--orders-border-radius);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.order-status--pending {
  background-color: var(--orders-warning-yellow);
  color: white;
}

.order-status--processing {
  background-color: var(--orders-processing-purple);
  color: white;
}

.order-status--completed {
  background-color: var(--orders-success-green);
  color: white;
}

.order-status--unknown {
  background-color: var(--orders-text-muted);
  color: white;
}

.order-status-icon {
  font-size: 1rem;
}

/* Order Summary */
.order-summary {
  background-color: var(--orders-bg-primary);
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius-lg);
  box-shadow: var(--orders-shadow);
}

.summary-card {
  padding: 2rem;
}

.summary-card h3 {
  color: var(--orders-text-primary);
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  border-bottom: 1px solid var(--orders-border);
  padding-bottom: 0.75rem;
}

.summary-details {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--orders-text-muted);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--orders-text-primary);
}

.summary-value--amount {
  color: var(--orders-primary-blue);
  font-size: 1.5rem;
}

.summary-value--status {
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.summary-value--pending {
  color: var(--orders-warning-yellow);
}

.summary-value--processing {
  color: var(--orders-processing-purple);
}

.summary-value--completed {
  color: var(--orders-success-green);
}

/* Customer Information */
.customer-info {
  background-color: var(--orders-bg-primary);
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius-lg);
  padding: 2rem;
  box-shadow: var(--orders-shadow);
}

.customer-info h3 {
  color: var(--orders-text-primary);
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  border-bottom: 1px solid var(--orders-border);
  padding-bottom: 0.75rem;
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.customer-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-label {
  font-size: 0.875rem;
  color: var(--orders-text-muted);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.customer-value {
  color: var(--orders-text-primary);
  font-weight: 500;
  line-height: 1.5;
}

/* Order Items */
.order-items {
  background-color: var(--orders-bg-primary);
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius-lg);
  padding: 2rem;
  box-shadow: var(--orders-shadow);
}

.order-items h3 {
  color: var(--orders-text-primary);
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  border-bottom: 1px solid var(--orders-border);
  padding-bottom: 0.75rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.item-card {
  background-color: var(--orders-bg-secondary);
  border: 1px solid var(--orders-border);
  border-radius: var(--orders-border-radius);
  padding: 1.5rem;
}

.item-info {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--orders-border-light);
}

.item-product,
.item-variation {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-details {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
}

.item-quantity,
.item-price,
.item-total {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: center;
}

.item-label {
  font-size: 0.75rem;
  color: var(--orders-text-muted);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.item-value {
  color: var(--orders-text-primary);
  font-weight: 500;
}

.item-value--price,
.item-value--total {
  color: var(--orders-primary-blue);
  font-weight: 600;
}

.item-value--total {
  font-size: 1.125rem;
}

.no-items {
  text-align: center;
  padding: 2rem;
  color: var(--orders-text-secondary);
}

/* Order Actions */
.order-actions {
  display: flex;
  gap: 1rem;
  padding: 2rem;
  background-color: var(--orders-bg-secondary);
  border-radius: var(--orders-border-radius-lg);
  border: 1px solid var(--orders-border);
}

.action-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--orders-border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--orders-transition);
  border: 1px solid;
  flex: 1;
}

.action-button--primary {
  background-color: var(--orders-primary-blue);
  color: white;
  border-color: var(--orders-primary-blue);
}

.action-button--primary:hover {
  background-color: var(--orders-secondary-blue);
  border-color: var(--orders-secondary-blue);
}

.action-button--secondary {
  background-color: transparent;
  color: var(--orders-primary-blue);
  border-color: var(--orders-primary-blue);
}

.action-button--secondary:hover {
  background-color: var(--orders-light-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .summary-details {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .customer-details {
    gap: 1.5rem;
  }

  .item-info {
    flex-direction: column;
    gap: 1rem;
  }

  .item-details {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: left;
  }

  .order-actions {
    flex-direction: column;
  }

  .action-button {
    flex: none;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    font-size: 0.75rem;
  }

  .order-title {
    font-size: 1.5rem;
  }

  .summary-item,
  .item-quantity,
  .item-price,
  .item-total {
    text-align: left;
  }
}

/* Print Styles */
@media print {
  .breadcrumb,
  .order-actions {
    display: none;
  }

  .order-detail-container {
    max-width: none;
    margin: 0;
    padding: 0;
  }

  .order-detail {
    gap: 1rem;
  }

  .order-header,
  .order-summary,
  .customer-info,
  .order-items {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }
}
