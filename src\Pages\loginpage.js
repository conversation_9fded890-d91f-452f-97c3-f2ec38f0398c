import React, { useState } from 'react';
import './loginpage.css';
import { useNavigate } from 'react-router-dom';

function Loginpage() {
    const navigate = useNavigate();
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");

    const handleLogin = async (e) => {
        e.preventDefault();
        try {
            const response = await fetch("https://ecommerce.techtrendo.com/api/token/", {
                method: "POST",
                headers: {
                    "Content-Type": 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });
            if (!response.ok) {
                throw new Error("Login failed");
            }
            const data = await response.json();
            if (data.access) {
                localStorage.setItem("access_token", data.access);
            }
            if (data.refresh) {
                localStorage.setItem("refresh_token", data.refresh);
            }
            navigate("/");
        } catch (err) {
            setError(err.message);
        }
    };

    return (
        <div className="login-container">
            <div className="login-title">Sign In</div>
            <form className="login-form" onSubmit={handleLogin}>
                <input
                    className="login-input"
                    type="text"
                    placeholder="Username"
                    value={username}
                    onChange={e => setUsername(e.target.value)}
                    autoComplete="username"
                />
                <input
                    className="login-input"
                    type="password"
                    placeholder="Password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    autoComplete="current-password"
                />
                <button className="login-button" type="submit">Login</button>
            </form>
            {error && <div className="login-error">{error}</div>}
        </div>
    );
}

export default Loginpage;
