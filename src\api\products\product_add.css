/* Product Add - Consistent with Orders & Login UI */

:root {
  --products-primary-blue: #2563eb;
  --products-secondary-blue: #3b82f6;
  --products-light-blue: #dbeafe;
  --products-success-green: #10b981;
  --products-warning-yellow: #f59e0b;
  --products-danger-red: #ef4444;
  --products-processing-purple: #8b5cf6;
  --products-text-primary: #1e293b;
  --products-text-secondary: #64748b;
  --products-text-muted: #94a3b8;
  --products-bg-primary: #ffffff;
  --products-bg-secondary: #f8fafc;
  --products-bg-tertiary: #f1f5f9;
  --products-border: #e2e8f0;
  --products-border-light: #f1f5f9;
  --products-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --products-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --products-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --products-transition: all 0.2s ease-in-out;
  --products-border-radius: 0.5rem;
  --products-border-radius-lg: 0.75rem;
}

.product-add-container {
  max-width: 600px;
  margin: 3rem auto;
  background: var(--products-bg-primary);
  border-radius: var(--products-border-radius);
  box-shadow: var(--products-shadow);
  padding: 2.5rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.product-add-title {
  color: var(--products-primary-blue);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  letter-spacing: -1px;
}


.product-add-form {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem 2rem;
  align-items: end;
}

.product-add-form .product-add-form-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  grid-column: span 1;
}

.product-add-form .product-add-form-row-full {
  grid-column: 1 / -1;
}

.product-add-label {
  color: var(--products-text-primary);
  font-weight: 500;
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.product-add-input, .product-add-textarea, .product-add-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--products-border);
  border-radius: var(--products-border-radius);
  font-size: 1rem;
  color: var(--products-text-primary);
  background: var(--products-bg-secondary);
  transition: var(--products-transition);
  width: 100%;
  box-sizing: border-box;
}

.product-add-input:focus, .product-add-textarea:focus, .product-add-select:focus {
  outline: none;
  border-color: var(--products-primary-blue);
  background: var(--products-bg-tertiary);
}

.product-add-checkbox {
  accent-color: var(--products-primary-blue);
  margin-left: 0.5rem;
  transform: scale(1.2);
}

.product-add-submit-btn {
  background: linear-gradient(90deg, var(--products-primary-blue), var(--products-secondary-blue));
  color: #fff;
  border: none;
  border-radius: var(--products-border-radius);
  padding: 0.85rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 2.5rem;
  cursor: pointer;
  transition: var(--products-transition);
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
  letter-spacing: 0.5px;
}

.product-add-submit-btn:hover {
  filter: brightness(0.95);
}
